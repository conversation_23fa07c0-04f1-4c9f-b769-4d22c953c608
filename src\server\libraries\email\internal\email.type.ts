export enum EmailType {
  DEFAULT = 'default',
  AUTHENTICATION_WELCOME = 'authentication.welcome.password',
  AUTHENTICATION_FORGOT_PASSWORD = 'authentication.forgot.password',
  AUTHORIZATION_VERIFICATION_CODE = 'authorization.verification.code',
  ADMIN_INVITATION = 'admin.invitation',
  USER_INVITATION = 'user.invitation',
}

export const EmailSender = {
  default: {
    email: '<EMAIL>',
    name: 'SalonQuip',
  },
}

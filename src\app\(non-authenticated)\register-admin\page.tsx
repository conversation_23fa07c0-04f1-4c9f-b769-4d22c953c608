"use client"
import { Api } from "@/core/trpc";
import { Button, Form, Input, Typography } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";

export default function RegisterAdminPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { enqueueSnackbar } = useSnackbar();
  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [email, setEmail] = useState("");
  const [token, setToken] = useState("");

  // Backend mutation for accepting invitation
  const { mutateAsync: acceptInvitation } = Api.authentication.acceptAdminInvitation.useMutation();

  useEffect(() => {
    const t = searchParams.get("token");
    if (t) {
      setToken(t);
      // Optionally, validate token with backend here and fetch email
      // For now, assume valid and let backend handle errors on submit
      setIsValid(true);
    }
  }, [searchParams]);

  const handleSubmit = async (values: { name: string; password: string }) => {
    setLoading(true);
    try {
      await acceptInvitation({ token, name: values.name, password: values.password });
      enqueueSnackbar("Admin registration successful! You can now log in.", { variant: "success" });
      router.push("/login");
    } catch (error: any) {
      enqueueSnackbar(error?.message || "Registration failed.", { variant: "error" });
      setLoading(false);
    }
  };

  if (!isValid) {
    return <Typography.Text type="danger">Invalid or missing invitation token.</Typography.Text>;
  }

  return (
    <div style={{ maxWidth: 400, margin: "auto", padding: 32 }}>
      <Typography.Title level={3}>Admin Registration</Typography.Title>
      <Typography.Paragraph>Set your name and password to complete your admin registration.</Typography.Paragraph>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item label="Name" name="name" rules={[{ required: true, message: "Name is required" }]}> <Input disabled={isLoading} /> </Form.Item>
        <Form.Item label="Password" name="password" rules={[{ required: true, message: "Password is required" }, { min: 8, message: "Password must be at least 8 characters" }]}> <Input.Password disabled={isLoading} /> </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" loading={isLoading} block>Register as Admin</Button>
        </Form.Item>
      </Form>
    </div>
  );
} 
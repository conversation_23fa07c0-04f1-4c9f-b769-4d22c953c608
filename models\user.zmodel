import "./account.zmodel";
import "./session.zmodel";
import "./role.zmodel";

import './admin.zmodel'
import './sale.zmodel'
import './inventoryLog.zmodel'

model User {
    id               String    @id @default(uuid())
    name             String?
    email            String?   @unique
    pictureUrl       String?
    stripeCustomerId String?
    password         String?   @password @omit
    dateCreated      DateTime  @default(now())
    dateUpdated      DateTime  @updatedAt @default(now())
    accounts         Account[]
    sessions         Session[]
    roles            Role[]
    admins           Admin[]   @relation("user")
    sales            Sale[]    @relation("user")
    inventoryLogs    InventoryLog[]

@@auth()
    @@allow("all", auth().id == this.id)
    @@allow('all', auth().roles?[name == 'admin'])
    @@allow("create", true)
}
'use client'

import { useUserContext } from '@/core/context'
import { Api } from '@/core/trpc'
import { PageLayout } from '@/designSystem/layouts/Page.layout'
import {
  CloudDownloadOutlined,
  CloudUploadOutlined,
  DatabaseOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  UploadOutlined,
  WarningOutlined
} from '@ant-design/icons'
import {
  Alert,
  Button,
  Card,
  Checkbox,
  Col,
  Divider,
  Modal,
  Progress,
  Row,
  Space,
  Statistic,
  Steps,
  Typography,
  Upload,
  message
} from 'antd'
import { useSnackbar } from 'notistack'
import { useState } from 'react'

const { Title, Text, Paragraph } = Typography
const { Step } = Steps

export default function SettingsPage() {
  const { checkRole } = useUserContext()
  const isAdmin = checkRole('admin')
  const { enqueueSnackbar } = useSnackbar()

  // Backup states
  const [backupOptions, setBackupOptions] = useState({
    includeUsers: false,
    includeBranches: true,
    includeItems: true,
    includeSales: true,
    includeCustomers: true,
    includeInventoryLogs: true,
  })
  const [isCreatingBackup, setIsCreatingBackup] = useState(false)
  const [backupProgress, setBackupProgress] = useState(0)

  // Restore states
  const [restoreOptions, setRestoreOptions] = useState({
    clearExistingData: false,
    restoreUsers: false,
    restoreBranches: true,
    restoreItems: true,
    restoreSales: true,
    restoreCustomers: true,
  })
  const [isRestoring, setIsRestoring] = useState(false)
  const [restoreFile, setRestoreFile] = useState(null)
  const [showRestoreModal, setShowRestoreModal] = useState(false)

  // API calls
  const { data: backupInfo, refetch: refetchBackupInfo } = Api.backup.getBackupInfo.useQuery(
    undefined,
    { enabled: isAdmin }
  )
  const createBackupMutation = Api.backup.createBackup.useMutation()
  const restoreBackupMutation = Api.backup.restoreBackup.useMutation()

  const handleCreateBackup = async () => {
    try {
      setIsCreatingBackup(true)
      setBackupProgress(10)

      const result = await createBackupMutation.mutateAsync(backupOptions)
      setBackupProgress(50)

      if (result.success) {
        // Create and download the backup file
        const dataStr = JSON.stringify(result.backup, null, 2)
        const dataBlob = new Blob([dataStr], { type: 'application/json' })
        const url = URL.createObjectURL(dataBlob)
        
        const link = document.createElement('a')
        link.href = url
        link.download = result.filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        setBackupProgress(100)
        enqueueSnackbar('Backup created and downloaded successfully!', { variant: 'success' })
        refetchBackupInfo()
      }
    } catch (error) {
      enqueueSnackbar(`Backup failed: ${error.message}`, { variant: 'error' })
    } finally {
      setIsCreatingBackup(false)
      setTimeout(() => setBackupProgress(0), 2000)
    }
  }

  const handleFileUpload = (file) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const backupData = JSON.parse(e.target.result as string)
        setRestoreFile(backupData)
        setShowRestoreModal(true)
      } catch (error) {
        enqueueSnackbar('Invalid backup file format', { variant: 'error' })
      }
    }
    reader.readAsText(file)
    return false // Prevent default upload
  }

  const handleRestore = async () => {
    if (!restoreFile) return

    Modal.confirm({
      title: 'Confirm Data Restore',
      content: (
        <div>
          <Paragraph>
            <WarningOutlined style={{ color: '#faad14', marginRight: 8 }} />
            This action will restore data from the backup file. 
            {restoreOptions.clearExistingData && (
              <Text type="danger"> All existing data will be permanently deleted!</Text>
            )}
          </Paragraph>
          <Paragraph>Are you sure you want to continue?</Paragraph>
        </div>
      ),
      okText: 'Yes, Restore Data',
      okType: 'danger',
      cancelText: 'Cancel',
      onOk: async () => {
        try {
          setIsRestoring(true)
          const result = await restoreBackupMutation.mutateAsync({
            backupData: restoreFile,
            ...restoreOptions
          })

          if (result.success) {
            enqueueSnackbar(result.message, { variant: 'success' })
            setShowRestoreModal(false)
            setRestoreFile(null)
            refetchBackupInfo()
          }
        } catch (error) {
          enqueueSnackbar(`Restore failed: ${error.message}`, { variant: 'error' })
        } finally {
          setIsRestoring(false)
        }
      }
    })
  }

  if (!isAdmin) {
    return (
      <PageLayout layout="full-width">
        <Alert
          message="Access Denied"
          description="Only administrators can access the settings page."
          type="error"
          showIcon
        />
      </PageLayout>
    )
  }

  return (
    <PageLayout layout="full-width">
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '16px',
        padding: '32px 24px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Title level={1} style={{ color: 'white', margin: 0 }}>
                <SettingOutlined style={{ marginRight: '12px' }} />
                System Settings
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: '16px' }}>
                Manage system backups and data restoration
              </Text>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Database Statistics */}
      {backupInfo && (
        <Card title={<><DatabaseOutlined /> Database Statistics</>} style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Users" value={backupInfo.stats.users} />
            </Col>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Branches" value={backupInfo.stats.branches} />
            </Col>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Items" value={backupInfo.stats.items} />
            </Col>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Customers" value={backupInfo.stats.customers} />
            </Col>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Sales" value={backupInfo.stats.sales} />
            </Col>
            <Col xs={12} sm={8} lg={4}>
              <Statistic title="Inventory Logs" value={backupInfo.stats.inventoryLogs} />
            </Col>
          </Row>
        </Card>
      )}

      <Row gutter={[24, 24]}>
        {/* Backup Section */}
        <Col xs={24} lg={12}>
          <Card
            title={<><CloudDownloadOutlined /> Create Backup</>}
            extra={
              <Button
                type="primary"
                icon={<DownloadOutlined />}
                onClick={handleCreateBackup}
                loading={isCreatingBackup}
                disabled={isCreatingBackup}
              >
                Create Backup
              </Button>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Backup Information"
                description="Create a complete backup of your salon data. The backup file will be downloaded to your device."
                type="info"
                showIcon
                icon={<InfoCircleOutlined />}
              />

              <Divider>Backup Options</Divider>
              
              <Checkbox
                checked={backupOptions.includeBranches}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeBranches: e.target.checked }))}
              >
                Include Branches
              </Checkbox>
              
              <Checkbox
                checked={backupOptions.includeItems}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeItems: e.target.checked }))}
              >
                Include Items & Inventory
              </Checkbox>
              
              <Checkbox
                checked={backupOptions.includeCustomers}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeCustomers: e.target.checked }))}
              >
                Include Customers
              </Checkbox>
              
              <Checkbox
                checked={backupOptions.includeSales}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeSales: e.target.checked }))}
              >
                Include Sales Data
              </Checkbox>
              
              <Checkbox
                checked={backupOptions.includeInventoryLogs}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeInventoryLogs: e.target.checked }))}
              >
                Include Inventory Logs
              </Checkbox>
              
              <Checkbox
                checked={backupOptions.includeUsers}
                onChange={(e) => setBackupOptions(prev => ({ ...prev, includeUsers: e.target.checked }))}
              >
                Include Users (Admin accounts)
              </Checkbox>

              {isCreatingBackup && (
                <Progress 
                  percent={backupProgress} 
                  status={backupProgress === 100 ? 'success' : 'active'}
                  strokeColor={{
                    '0%': '#667eea',
                    '100%': '#764ba2',
                  }}
                />
              )}
            </Space>
          </Card>
        </Col>

        {/* Restore Section */}
        <Col xs={24} lg={12}>
          <Card
            title={<><CloudUploadOutlined /> Restore Backup</>}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Alert
                message="Restore Warning"
                description="Restoring a backup will modify your current data. Please ensure you have a recent backup before proceeding."
                type="warning"
                showIcon
                icon={<ExclamationCircleOutlined />}
              />

              <Upload.Dragger
                accept=".json"
                beforeUpload={handleFileUpload}
                showUploadList={false}
                style={{ marginBottom: '16px' }}
              >
                <p className="ant-upload-drag-icon">
                  <UploadOutlined />
                </p>
                <p className="ant-upload-text">Click or drag backup file to upload</p>
                <p className="ant-upload-hint">
                  Support for .json backup files only
                </p>
              </Upload.Dragger>

              {restoreFile && (
                <Alert
                  message="Backup File Loaded"
                  description={`Backup created on ${new Date(restoreFile.metadata?.createdAt).toLocaleString()}`}
                  type="success"
                  showIcon
                  action={
                    <Button size="small" onClick={() => setShowRestoreModal(true)}>
                      Configure Restore
                    </Button>
                  }
                />
              )}
            </Space>
          </Card>
        </Col>
      </Row>

      {/* Restore Configuration Modal */}
      <Modal
        title="Configure Data Restore"
        open={showRestoreModal}
        onCancel={() => setShowRestoreModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowRestoreModal(false)}>
            Cancel
          </Button>,
          <Button
            key="restore"
            type="primary"
            danger={restoreOptions.clearExistingData}
            onClick={handleRestore}
            loading={isRestoring}
            icon={<CloudUploadOutlined />}
          >
            Restore Data
          </Button>
        ]}
        width={600}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="Restore Configuration"
            description="Choose which data to restore and whether to clear existing data."
            type="info"
            showIcon
          />

          <Divider>Restore Options</Divider>
          
          <Checkbox
            checked={restoreOptions.clearExistingData}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, clearExistingData: e.target.checked }))}
          >
            <Text type="danger">Clear all existing data before restore</Text>
          </Checkbox>
          
          <Divider />
          
          <Checkbox
            checked={restoreOptions.restoreBranches}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, restoreBranches: e.target.checked }))}
          >
            Restore Branches
          </Checkbox>
          
          <Checkbox
            checked={restoreOptions.restoreItems}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, restoreItems: e.target.checked }))}
          >
            Restore Items & Inventory
          </Checkbox>
          
          <Checkbox
            checked={restoreOptions.restoreCustomers}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, restoreCustomers: e.target.checked }))}
          >
            Restore Customers
          </Checkbox>
          
          <Checkbox
            checked={restoreOptions.restoreSales}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, restoreSales: e.target.checked }))}
          >
            Restore Sales Data
          </Checkbox>
          
          <Checkbox
            checked={restoreOptions.restoreUsers}
            onChange={(e) => setRestoreOptions(prev => ({ ...prev, restoreUsers: e.target.checked }))}
          >
            <Text type="danger">Restore Users (This will affect admin accounts)</Text>
          </Checkbox>

          {restoreOptions.clearExistingData && (
            <Alert
              message="Data Loss Warning"
              description="All existing data will be permanently deleted. This action cannot be undone."
              type="error"
              showIcon
            />
          )}
        </Space>
      </Modal>
    </PageLayout>
  )
}

<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="200" height="200" rx="20" fill="url(#gradient)"/>

  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Salon Chair Icon -->
  <g transform="translate(60, 50)">
    <!-- Chair Back -->
    <rect x="20" y="10" width="40" height="50" rx="20" fill="white" opacity="0.95"/>

    <!-- Chair Seat -->
    <ellipse cx="40" cy="65" rx="30" ry="12" fill="white" opacity="0.95"/>

    <!-- Chair Base -->
    <rect x="36" y="77" width="8" height="25" fill="white" opacity="0.9"/>
    <ellipse cx="40" cy="105" rx="20" ry="6" fill="white" opacity="0.9"/>

    <!-- Armrests -->
    <rect x="8" y="30" width="12" height="20" rx="6" fill="white" opacity="0.8"/>
    <rect x="60" y="30" width="12" height="20" rx="6" fill="white" opacity="0.8"/>
  </g>

  <!-- Company Name -->
  <text x="100" y="160" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" font-weight="bold">SalonQuip</text>

  <!-- Tagline -->
  <text x="100" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" opacity="0.8">Professional Salon Management</text>
</svg>

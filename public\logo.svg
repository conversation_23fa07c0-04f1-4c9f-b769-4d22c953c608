<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="90" fill="#667eea" stroke="#764ba2" stroke-width="4"/>
  
  <!-- <PERSON> Chair Icon -->
  <g transform="translate(50, 40)">
    <!-- Chair Back -->
    <rect x="30" y="20" width="40" height="60" rx="20" fill="white" opacity="0.9"/>
    
    <!-- Chair Seat -->
    <ellipse cx="50" cy="85" rx="35" ry="15" fill="white" opacity="0.9"/>
    
    <!-- Chair Base -->
    <rect x="45" y="100" width="10" height="30" fill="white" opacity="0.8"/>
    <ellipse cx="50" cy="135" rx="25" ry="8" fill="white" opacity="0.8"/>
    
    <!-- Armrests -->
    <rect x="15" y="45" width="15" height="25" rx="7" fill="white" opacity="0.7"/>
    <rect x="70" y="45" width="15" height="25" rx="7" fill="white" opacity="0.7"/>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="40" cy="60" r="3" fill="white" opacity="0.6"/>
  <circle cx="160" cy="140" r="3" fill="white" opacity="0.6"/>
  <circle cx="170" cy="70" r="2" fill="white" opacity="0.5"/>
  <circle cx="30" cy="150" r="2" fill="white" opacity="0.5"/>
  
  <!-- Text -->
  <text x="100" y="180" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">SalonQuip</text>
</svg>

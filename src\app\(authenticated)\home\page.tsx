'use client'

import { useUserContext } from '@/core/context'
import { Api } from '@/core/trpc'
import { PageLayout } from '@/designSystem/layouts/Page.layout'
import {
    BarChartOutlined,
    CalendarOutlined,
    CheckCircleOutlined,
    DashboardOutlined,
    DollarOutlined,
    ExclamationCircleOutlined,
    RiseOutlined,
    ShoppingCartOutlined,
    TeamOutlined,
    TrophyOutlined
} from '@ant-design/icons'
import {
    Avatar,
    Card,
    Col,
    DatePicker,
    Divider,
    List,
    Row,
    Space,
    Statistic,
    Tag,
    Typography
} from 'antd'
import dayjs from 'dayjs'
import { useSnackbar } from 'notistack'
import { useEffect, useMemo, useState } from 'react'
import {
    Area,
    AreaChart,
    CartesianGrid,
    Legend,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    YAxis
} from 'recharts'

const { Title, Text } = Typography
const { RangePicker } = DatePicker

// Define colors for charts
const COLORS = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'];

export default function HomePage() {
  const { user, checkRole } = useUserContext()
  const { enqueueSnackbar } = useSnackbar()

  // State for responsive design and date filtering
  const [isMobile, setIsMobile] = useState(false)
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null)

  const isAdmin = checkRole('admin')

  // Handle window resize for responsive design
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Fetch essential data for dashboard
  const { data: salesData, isLoading: salesLoading } = Api.sale.findMany.useQuery({
    orderBy: { saleDate: 'desc' },
    take: 100 // Limit for performance
  });

  const { data: customers } = Api.customer.findMany.useQuery({});
  const { data: branches } = Api.branch.findMany.useQuery({});
  const { data: items } = Api.item.findMany.useQuery({
    include: { branch: true }
  });

  // Calculate dashboard statistics
  const dashboardStats = useMemo(() => {
    const totalSales = salesData?.reduce((sum, sale) => sum + (sale.sellPrice * sale.quantitySold), 0) || 0;
    const totalProfit = salesData?.reduce((sum, sale) => sum + sale.profit, 0) || 0;
    const totalCustomers = customers?.length || 0;
    const totalBranches = branches?.length || 0;
    const totalItems = items?.length || 0;
    const lowStockItems = items?.filter(item => item.quantity < item.minimumStockLevel).length || 0;

    return {
      totalSales,
      totalProfit,
      totalCustomers,
      totalBranches,
      totalItems,
      lowStockItems
    };
  }, [salesData, customers, branches, items]);

  // Get recent sales for activity feed
  const recentSales = useMemo(() => {
    if (!salesData) return [];
    return salesData
      .sort((a, b) => new Date(b.saleDate).getTime() - new Date(a.saleDate).getTime())
      .slice(0, 5);
  }, [salesData]);

  // Get low stock items for alerts and display
  const lowStockItems = useMemo(() => {
    if (!items) return [];
    return items
      .filter(item => item.quantity < item.minimumStockLevel)
      .sort((a, b) => (a.quantity - a.minimumStockLevel) - (b.quantity - b.minimumStockLevel))
      .slice(0, 10); // Show top 10 most critical
  }, [items]);

  // Get out of stock items
  const outOfStockItems = useMemo(() => {
    if (!items) return [];
    return items.filter(item => item.quantity === 0);
  }, [items]);

  // Get top selling items
  const topSellingItems = useMemo(() => {
    if (!salesData) return [];

    const itemSales = new Map();
    salesData.forEach(sale => {
      const current = itemSales.get(sale.itemName) || 0;
      itemSales.set(sale.itemName, current + sale.quantitySold);
    });

    return Array.from(itemSales.entries())
      .map(([name, quantity]) => ({ name, quantity }))
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
  }, [salesData]);

  // Get sales trend data for the last 7 days
  const salesTrendData = useMemo(() => {
    if (!salesData) return [];

    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = dayjs().subtract(i, 'day');
      return {
        date: date.format('MMM DD'),
        sales: 0,
        profit: 0
      };
    }).reverse();

    salesData.forEach(sale => {
      const saleDate = dayjs(sale.saleDate);
      const dayIndex = last7Days.findIndex(day =>
        saleDate.format('MMM DD') === day.date
      );

      if (dayIndex !== -1) {
        last7Days[dayIndex].sales += sale.sellPrice * sale.quantitySold;
        last7Days[dayIndex].profit += sale.profit;
      }
    });

    return last7Days;
  }, [salesData]);

  return (
    <PageLayout layout="full-width">
      {/* Welcome Header */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '16px',
        padding: isMobile ? '24px 16px' : '32px 24px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle">
          <Col xs={24} lg={16}>
            <Space direction="vertical" size="small">
              <Title level={1} style={{ color: 'white', margin: 0, fontSize: isMobile ? '24px' : '32px' }}>
                <DashboardOutlined style={{ marginRight: '12px' }} />
                Welcome back, {user?.name || 'User'}!
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: isMobile ? '14px' : '16px' }}>
                Here's what's happening with your business today
              </Text>
            </Space>
          </Col>
          <Col xs={24} lg={8} style={{ textAlign: isMobile ? 'left' : 'right', marginTop: isMobile ? '16px' : '0' }}>
            <Space direction="vertical" size="small">
              <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                <CalendarOutlined style={{ marginRight: '8px' }} />
                {dayjs().format('dddd, MMMM D, YYYY')}
              </Text>
            </Space>
          </Col>
        </Row>
      </div>

      {/* Stock Alerts */}
      {(lowStockItems.length > 0 || outOfStockItems.length > 0) && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          {outOfStockItems.length > 0 && (
            <Col xs={24} lg={12}>
              <Card
                style={{
                  border: '1px solid #ff4d4f',
                  backgroundColor: '#fff2f0'
                }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <ExclamationCircleOutlined style={{ color: '#ff4d4f', fontSize: '20px' }} />
                    <div>
                      <Text strong style={{ color: '#ff4d4f', fontSize: '16px' }}>
                        Out of Stock Alert
                      </Text>
                      <br />
                      <Text type="secondary">
                        {outOfStockItems.length} items are completely out of stock
                      </Text>
                    </div>
                  </Space>
                  <Divider style={{ margin: '8px 0' }} />
                  <List
                    size="small"
                    dataSource={outOfStockItems.slice(0, 3)}
                    renderItem={(item) => (
                      <List.Item style={{ padding: '4px 0' }}>
                        <Space>
                          <Avatar
                            size="small"
                            style={{ backgroundColor: '#ff4d4f', fontSize: '12px' }}
                          >
                            0
                          </Avatar>
                          <div>
                            <Text strong>{item.name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {item.branch?.name} • {item.category}
                            </Text>
                          </div>
                        </Space>
                      </List.Item>
                    )}
                  />
                  {outOfStockItems.length > 3 && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      +{outOfStockItems.length - 3} more items out of stock
                    </Text>
                  )}
                </Space>
              </Card>
            </Col>
          )}

          {lowStockItems.length > 0 && (
            <Col xs={24} lg={outOfStockItems.length > 0 ? 12 : 24}>
              <Card
                style={{
                  border: '1px solid #faad14',
                  backgroundColor: '#fffbe6'
                }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '20px' }} />
                    <div>
                      <Text strong style={{ color: '#faad14', fontSize: '16px' }}>
                        Low Stock Alert
                      </Text>
                      <br />
                      <Text type="secondary">
                        {lowStockItems.length} items are running low on stock
                      </Text>
                    </div>
                  </Space>
                  <Divider style={{ margin: '8px 0' }} />
                  <List
                    size="small"
                    dataSource={lowStockItems.slice(0, 5)}
                    renderItem={(item) => (
                      <List.Item style={{ padding: '4px 0' }}>
                        <Space>
                          <Avatar
                            size="small"
                            style={{
                              backgroundColor: item.quantity === 0 ? '#ff4d4f' : '#faad14',
                              fontSize: '12px'
                            }}
                          >
                            {item.quantity}
                          </Avatar>
                          <div>
                            <Text strong>{item.name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {item.branch?.name} • Min: {item.minimumStockLevel} • {item.category}
                            </Text>
                          </div>
                        </Space>
                      </List.Item>
                    )}
                  />
                  {lowStockItems.length > 5 && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      +{lowStockItems.length - 5} more items need restocking
                    </Text>
                  )}
                </Space>
              </Card>
            </Col>
          )}
        </Row>
      )}

      {/* Key Metrics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Sales"
              value={dashboardStats.totalSales}
              precision={2}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              suffix="KES"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Profit"
              value={dashboardStats.totalProfit}
              precision={2}
              prefix={<RiseOutlined style={{ color: '#1890ff' }} />}
              suffix="KES"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Customers"
              value={dashboardStats.totalCustomers}
              prefix={<TeamOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Low Stock Items"
              value={dashboardStats.lowStockItems}
              prefix={<ExclamationCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: dashboardStats.lowStockItems > 0 ? '#faad14' : '#52c41a' }}
              suffix={dashboardStats.lowStockItems > 0 ? ' Need Attention' : ' All Good'}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts and Analytics */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {/* Sales Trend Chart */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <BarChartOutlined style={{ color: '#1890ff' }} />
                <span>Sales Trend (Last 7 Days)</span>
              </Space>
            }
          >
            <div style={{ height: '300px' }}>
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={salesTrendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip
                    formatter={(value, name) => [
                      `KES ${Number(value).toLocaleString()}`,
                      name === 'sales' ? 'Sales' : 'Profit'
                    ]}
                  />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stackId="1"
                    stroke="#1890ff"
                    fill="#1890ff"
                    fillOpacity={0.6}
                    name="Sales"
                  />
                  <Area
                    type="monotone"
                    dataKey="profit"
                    stackId="2"
                    stroke="#52c41a"
                    fill="#52c41a"
                    fillOpacity={0.6}
                    name="Profit"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>

        {/* Top Selling Items */}
        <Col xs={24} lg={8}>
          <Card
            title={
              <Space>
                <TrophyOutlined style={{ color: '#faad14' }} />
                <span>Top Selling Items</span>
              </Space>
            }
          >
            <List
              dataSource={topSellingItems}
              renderItem={(item, index) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{
                          backgroundColor: COLORS[index % COLORS.length],
                          color: 'white'
                        }}
                      >
                        {index + 1}
                      </Avatar>
                    }
                    title={item.name}
                    description={`${item.quantity} units sold`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Activity and Quick Stats */}
      <Row gutter={[16, 16]}>
        {/* Recent Sales Activity */}
        <Col xs={24} lg={16}>
          <Card
            title={
              <Space>
                <ShoppingCartOutlined style={{ color: '#52c41a' }} />
                <span>Recent Sales Activity</span>
              </Space>
            }
          >
            <List
              dataSource={recentSales}
              renderItem={(sale) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        style={{ backgroundColor: '#52c41a', color: 'white' }}
                        icon={<ShoppingCartOutlined />}
                      />
                    }
                    title={
                      <Space>
                        <span>{sale.itemName}</span>
                        <Tag color="green">KES {(sale.sellPrice * sale.quantitySold).toLocaleString()}</Tag>
                      </Space>
                    }
                    description={
                      <Space>
                        <span>Sold by: {sale.userName}</span>
                        <Divider type="vertical" />
                        <span>Qty: {sale.quantitySold}</span>
                        <Divider type="vertical" />
                        <span>{dayjs(sale.saleDate).format('MMM DD, YYYY HH:mm')}</span>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* Quick Stats */}
        <Col xs={24} lg={8}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            {/* Inventory Alert */}
            {dashboardStats.lowStockItems > 0 && (
              <Card>
                <Space>
                  <ExclamationCircleOutlined style={{ color: '#faad14', fontSize: '20px' }} />
                  <div>
                    <Text strong style={{ color: '#faad14' }}>Low Stock Alert</Text>
                    <br />
                    <Text type="secondary">
                      {dashboardStats.lowStockItems} items need restocking
                    </Text>
                  </div>
                </Space>
              </Card>
            )}

            {/* Performance Summary */}
            <Card title="Performance Summary">
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text type="secondary">Total Items</Text>
                  <br />
                  <Text strong style={{ fontSize: '18px' }}>{dashboardStats.totalItems}</Text>
                </div>
                <Divider style={{ margin: '8px 0' }} />
                <div>
                  <Text type="secondary">Profit Margin</Text>
                  <br />
                  <Text strong style={{ fontSize: '18px', color: '#52c41a' }}>
                    {dashboardStats.totalSales > 0
                      ? ((dashboardStats.totalProfit / dashboardStats.totalSales) * 100).toFixed(1)
                      : '0'
                    }%
                  </Text>
                </div>
                <Divider style={{ margin: '8px 0' }} />
                <div>
                  <Text type="secondary">Stock Status</Text>
                  <br />
                  <Space>
                    {dashboardStats.lowStockItems === 0 ? (
                      <>
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        <Text strong style={{ color: '#52c41a' }}>All Stock Levels Good</Text>
                      </>
                    ) : (
                      <>
                        <ExclamationCircleOutlined style={{ color: '#faad14' }} />
                        <Text strong style={{ color: '#faad14' }}>
                          {dashboardStats.lowStockItems} Items Low
                        </Text>
                      </>
                    )}
                  </Space>
                </div>
                <Divider style={{ margin: '8px 0' }} />
                <div>
                  <Text type="secondary">System Status</Text>
                  <br />
                  <Space>
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    <Text strong style={{ color: '#52c41a' }}>All Systems Operational</Text>
                  </Space>
                </div>
              </Space>
            </Card>
          </Space>
        </Col>
      </Row>
    </PageLayout>
  )
}
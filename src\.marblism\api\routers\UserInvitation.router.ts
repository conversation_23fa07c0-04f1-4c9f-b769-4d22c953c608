/* eslint-disable */
import { type RouterFactory, type ProcBuilder, type BaseConfig, db } from ".";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';
import type { Prisma } from '@prisma/client';
import type { UseTRPCMutationOptions, UseTRPCMutationResult, UseTRPCQueryOptions, UseTRPCQueryResult, UseTRPCInfiniteQueryOptions, UseTRPCInfiniteQueryResult } from '@trpc/react-query/shared';
import type { TRPCClientErrorLike } from '@trpc/client';
import type { AnyRouter } from '@trpc/server';

export default function createRouter<Config extends BaseConfig>(router: RouterFactory<Config>, procedure: ProcBuilder<Config>) {
    return router({

        create: procedure.input($Schema.UserInvitationInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).userInvitation.create(input as any))),

        delete: procedure.input($Schema.UserInvitationInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).userInvitation.delete(input as any))),

        findFirst: procedure.input($Schema.UserInvitationInputSchema.findFirst).query(({ ctx, input }) => checkRead(db(ctx).userInvitation.findFirst(input as any))),

        findMany: procedure.input($Schema.UserInvitationInputSchema.findMany).query(({ ctx, input }) => checkRead(db(ctx).userInvitation.findMany(input as any))),

        findUnique: procedure.input($Schema.UserInvitationInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).userInvitation.findUnique(input as any))),

        update: procedure.input($Schema.UserInvitationInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).userInvitation.update(input as any))),

    }
    );
}

export interface ClientType<AppRouter extends AnyRouter, Context = AppRouter['_def']['_config']['$types']['ctx']> {
    create: {

        useMutation: <T extends Prisma.UserInvitationCreateArgs>(opts?: UseTRPCMutationOptions<
            Prisma.UserInvitationCreateArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.UserInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.UserInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.UserInvitationCreateArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.UserInvitationCreateArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.UserInvitationGetPayload<T>, Context>) => Promise<Prisma.UserInvitationGetPayload<T>>
            };

    };
    delete: {

        useMutation: <T extends Prisma.UserInvitationDeleteArgs>(opts?: UseTRPCMutationOptions<
            Prisma.UserInvitationDeleteArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.UserInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.UserInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.UserInvitationDeleteArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.UserInvitationDeleteArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.UserInvitationGetPayload<T>, Context>) => Promise<Prisma.UserInvitationGetPayload<T>>
            };

    };
    findFirst: {

        useQuery: <T extends Prisma.UserInvitationFindFirstArgs, TData = Prisma.UserInvitationGetPayload<T>>(
            input: Prisma.SelectSubset<T, Prisma.UserInvitationFindFirstArgs>,
            opts?: UseTRPCQueryOptions<string, T, Prisma.UserInvitationGetPayload<T>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.UserInvitationFindFirstArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.UserInvitationFindFirstArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Prisma.UserInvitationGetPayload<T>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Prisma.UserInvitationGetPayload<T>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    findMany: {

        useQuery: <T extends Prisma.UserInvitationFindManyArgs, TData = Array<Prisma.UserInvitationGetPayload<T>>>(
            input: Prisma.SelectSubset<T, Prisma.UserInvitationFindManyArgs>,
            opts?: UseTRPCQueryOptions<string, T, Array<Prisma.UserInvitationGetPayload<T>>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.UserInvitationFindManyArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.UserInvitationFindManyArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Array<Prisma.UserInvitationGetPayload<T>>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Array<Prisma.UserInvitationGetPayload<T>>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    findUnique: {

        useQuery: <T extends Prisma.UserInvitationFindUniqueArgs, TData = Prisma.UserInvitationGetPayload<T>>(
            input: Prisma.SelectSubset<T, Prisma.UserInvitationFindUniqueArgs>,
            opts?: UseTRPCQueryOptions<string, T, Prisma.UserInvitationGetPayload<T>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.UserInvitationFindUniqueArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.UserInvitationFindUniqueArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Prisma.UserInvitationGetPayload<T>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Prisma.UserInvitationGetPayload<T>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    update: {

        useMutation: <T extends Prisma.UserInvitationUpdateArgs>(opts?: UseTRPCMutationOptions<
            Prisma.UserInvitationUpdateArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.UserInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.UserInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.UserInvitationUpdateArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.UserInvitationUpdateArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.UserInvitationGetPayload<T>, Context>) => Promise<Prisma.UserInvitationGetPayload<T>>
            };

    };
}

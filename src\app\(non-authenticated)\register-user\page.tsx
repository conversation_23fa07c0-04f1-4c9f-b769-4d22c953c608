"use client"
import { Api } from "@/core/trpc";
import { Button, Form, Input, Typography, Alert, Space, Tag } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { UserOutlined, LockOutlined, MailOutlined, CrownOutlined } from "@ant-design/icons";

const { Title, Text } = Typography;

export default function RegisterUserPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { enqueueSnackbar } = useSnackbar();
  const [form] = Form.useForm();
  const [isLoading, setLoading] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [email, setEmail] = useState("");
  const [userType, setUserType] = useState("");
  const [token, setToken] = useState("");

  // Backend mutation for accepting invitation
  const { mutateAsync: acceptInvitation } = Api.authentication.acceptUserInvitation.useMutation();

  useEffect(() => {
    const t = searchParams.get("token");
    if (t) {
      setToken(t);
      // Optionally, validate token with backend here and fetch email
      // For now, assume valid and let backend handle errors on submit
      setIsValid(true);
    }
  }, [searchParams]);

  const handleSubmit = async (values: { name: string; password: string }) => {
    if (!token) {
      enqueueSnackbar("Invalid invitation token", { variant: "error" });
      return;
    }

    setLoading(true);
    try {
      const result = await acceptInvitation({
        token,
        name: values.name,
        password: values.password,
      });

      if (result.success) {
        enqueueSnackbar(
          `Account created successfully! You can now sign in${result.userType === 'admin' ? ' as an administrator' : ''}.`,
          { variant: "success" }
        );
        router.push("/login");
      }
    } catch (error: any) {
      enqueueSnackbar(error.message || "Failed to create account", {
        variant: "error",
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isValid) {
    return (
      <div style={{ 
        minHeight: "100vh", 
        display: "flex", 
        alignItems: "center", 
        justifyContent: "center",
        background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      }}>
        <div style={{
          background: "white",
          padding: "40px",
          borderRadius: "12px",
          boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
          maxWidth: "400px",
          width: "100%",
          margin: "20px"
        }}>
          <Alert
            message="Invalid Invitation"
            description="The invitation link is invalid or has expired. Please contact your administrator for a new invitation."
            type="error"
            showIcon
          />
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: "100vh", 
      display: "flex", 
      alignItems: "center", 
      justifyContent: "center",
      background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    }}>
      <div style={{
        background: "white",
        padding: "40px",
        borderRadius: "12px",
        boxShadow: "0 8px 32px rgba(0,0,0,0.1)",
        maxWidth: "400px",
        width: "100%",
        margin: "20px"
      }}>
        <div style={{ textAlign: "center", marginBottom: "32px" }}>
          <div style={{
            width: "60px",
            height: "60px",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "0 auto 16px",
            color: "white",
            fontSize: "24px",
            fontWeight: "bold"
          }}>
            SQ
          </div>
          <Title level={2} style={{ margin: 0, color: "#333" }}>
            Complete Your Registration
          </Title>
          <Text type="secondary" style={{ fontSize: "16px" }}>
            You've been invited to join SalonQuip
          </Text>
          {userType && (
            <div style={{ marginTop: "8px" }}>
              <Tag 
                color={userType === 'admin' ? 'red' : 'blue'} 
                icon={userType === 'admin' ? <CrownOutlined /> : <UserOutlined />}
                style={{ fontSize: "12px" }}
              >
                {userType === 'admin' ? 'Administrator Account' : 'User Account'}
              </Tag>
            </div>
          )}
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="large"
        >
          <Form.Item
            label="Full Name"
            name="name"
            rules={[
              { required: true, message: "Please enter your full name" },
              { min: 2, message: "Name must be at least 2 characters" }
            ]}
          >
            <Input 
              prefix={<UserOutlined />} 
              placeholder="Enter your full name"
              disabled={isLoading}
            />
          </Form.Item>

          <Form.Item
            label="Password"
            name="password"
            rules={[
              { required: true, message: "Please enter a password" },
              { min: 8, message: "Password must be at least 8 characters" }
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="Create a secure password"
              disabled={isLoading}
            />
          </Form.Item>

          <Form.Item
            label="Confirm Password"
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: "Please confirm your password" },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('Passwords do not match'));
                },
              }),
            ]}
          >
            <Input.Password 
              prefix={<LockOutlined />} 
              placeholder="Confirm your password"
              disabled={isLoading}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: "16px" }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              block
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                border: "none",
                height: "48px",
                fontSize: "16px",
                fontWeight: "600"
              }}
            >
              Create Account
            </Button>
          </Form.Item>
        </Form>

        <div style={{ textAlign: "center", marginTop: "24px" }}>
          <Text type="secondary" style={{ fontSize: "14px" }}>
            Already have an account?{" "}
            <a 
              href="/login" 
              style={{ 
                color: "#667eea", 
                textDecoration: "none",
                fontWeight: "500"
              }}
            >
              Sign in here
            </a>
          </Text>
        </div>
      </div>
    </div>
  );
}

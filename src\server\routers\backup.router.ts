import { Trpc } from '@/core/trpc/server'
import { TRPCError } from '@trpc/server'
import { z } from 'zod'

export const BackupRouter = Trpc.createRouter({
  // Create a full backup of all data
  createBackup: Trpc.procedurePrivate
    .input(z.object({
      includeUsers: z.boolean().default(true),
      includeBranches: z.boolean().default(true),
      includeItems: z.boolean().default(true),
      includeSales: z.boolean().default(true),
      includeCustomers: z.boolean().default(true),
      includeInventoryLogs: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if user is admin
        const user = await ctx.databaseUnprotected.user.findUnique({
          where: { id: ctx.session.user.id },
          include: { roles: true }
        })

        const isAdmin = user?.roles?.some(role => role.name === 'admin')
        if (!isAdmin) {
          throw new TRPCError({ code: 'FORBIDDEN', message: 'Only admins can create backups' })
        }

        const backup: any = {
          metadata: {
            version: '1.0.0',
            createdAt: new Date().toISOString(),
            createdBy: ctx.session.user.id,
            createdByName: user.name || user.email,
          },
          data: {}
        }

        // Backup Users (excluding passwords)
        if (input.includeUsers) {
          const users = await ctx.databaseUnprotected.user.findMany({
            include: {
              roles: true,
              admins: true,
            },
            omit: {
              password: true
            }
          })
          backup.data.users = users
        }

        // Backup Branches
        if (input.includeBranches) {
          const branches = await ctx.databaseUnprotected.branch.findMany()
          backup.data.branches = branches
        }

        // Backup Items
        if (input.includeItems) {
          const items = await ctx.databaseUnprotected.item.findMany({
            include: {
              branch: true
            }
          })
          backup.data.items = items
        }

        // Backup Customers
        if (input.includeCustomers) {
          const customers = await ctx.databaseUnprotected.customer.findMany()
          backup.data.customers = customers
        }

        // Backup Sales
        if (input.includeSales) {
          const sales = await ctx.databaseUnprotected.sale.findMany({
            include: {
              saleItems: {
                include: {
                  item: true
                }
              },
              customer: true,
              user: true
            }
          })
          backup.data.sales = sales
        }

        // Backup Inventory Logs
        if (input.includeInventoryLogs) {
          const inventoryLogs = await ctx.databaseUnprotected.inventoryLog.findMany({
            include: {
              item: true,
              user: true
            }
          })
          backup.data.inventoryLogs = inventoryLogs
        }

        // Backup Stock Transfers
        const stockTransfers = await ctx.databaseUnprotected.stockTransfer.findMany({
          include: {
            item: true,
            fromBranch: true,
            toBranch: true
          }
        })
        backup.data.stockTransfers = stockTransfers

        return {
          success: true,
          backup,
          filename: `salonquip-backup-${new Date().toISOString().split('T')[0]}.json`
        }
      } catch (error) {
        console.error('Backup creation failed:', error)
        throw new TRPCError({ 
          code: 'INTERNAL_SERVER_ERROR', 
          message: 'Failed to create backup: ' + error.message 
        })
      }
    }),

  // Restore data from backup
  restoreBackup: Trpc.procedurePrivate
    .input(z.object({
      backupData: z.any(),
      clearExistingData: z.boolean().default(false),
      restoreUsers: z.boolean().default(false),
      restoreBranches: z.boolean().default(true),
      restoreItems: z.boolean().default(true),
      restoreSales: z.boolean().default(true),
      restoreCustomers: z.boolean().default(true),
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if user is admin
        const user = await ctx.databaseUnprotected.user.findUnique({
          where: { id: ctx.session.user.id },
          include: { roles: true }
        })

        const isAdmin = user?.roles?.some(role => role.name === 'admin')
        if (!isAdmin) {
          throw new TRPCError({ code: 'FORBIDDEN', message: 'Only admins can restore backups' })
        }

        const backup = input.backupData
        
        // Validate backup format
        if (!backup.metadata || !backup.data) {
          throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid backup format' })
        }

        const results = {
          restored: {
            branches: 0,
            items: 0,
            customers: 0,
            sales: 0,
            users: 0
          },
          errors: []
        }

        // Clear existing data if requested
        if (input.clearExistingData) {
          await ctx.databaseUnprotected.saleItem.deleteMany()
          await ctx.databaseUnprotected.sale.deleteMany()
          await ctx.databaseUnprotected.inventoryLog.deleteMany()
          await ctx.databaseUnprotected.stockTransfer.deleteMany()
          await ctx.databaseUnprotected.item.deleteMany()
          await ctx.databaseUnprotected.customer.deleteMany()
          await ctx.databaseUnprotected.branch.deleteMany()
          
          if (input.restoreUsers) {
            await ctx.databaseUnprotected.admin.deleteMany()
            await ctx.databaseUnprotected.role.deleteMany()
            await ctx.databaseUnprotected.user.deleteMany()
          }
        }

        // Restore Branches first (dependencies)
        if (input.restoreBranches && backup.data.branches) {
          for (const branch of backup.data.branches) {
            try {
              await ctx.databaseUnprotected.branch.upsert({
                where: { id: branch.id },
                update: {
                  name: branch.name,
                  location: branch.location,
                  dateUpdated: new Date()
                },
                create: {
                  id: branch.id,
                  name: branch.name,
                  location: branch.location,
                  dateCreated: new Date(branch.dateCreated),
                  dateUpdated: new Date()
                }
              })
              results.restored.branches++
            } catch (error) {
              results.errors.push(`Branch ${branch.name}: ${error.message}`)
            }
          }
        }

        // Restore Customers
        if (input.restoreCustomers && backup.data.customers) {
          for (const customer of backup.data.customers) {
            try {
              await ctx.databaseUnprotected.customer.upsert({
                where: { id: customer.id },
                update: {
                  name: customer.name,
                  phoneNumber: customer.phoneNumber,
                  loyaltyPoints: customer.loyaltyPoints,
                  referredBy: customer.referredBy,
                  dateUpdated: new Date()
                },
                create: {
                  id: customer.id,
                  name: customer.name,
                  phoneNumber: customer.phoneNumber,
                  loyaltyPoints: customer.loyaltyPoints,
                  referredBy: customer.referredBy,
                  dateCreated: new Date(customer.dateCreated),
                  dateUpdated: new Date()
                }
              })
              results.restored.customers++
            } catch (error) {
              results.errors.push(`Customer ${customer.name}: ${error.message}`)
            }
          }
        }

        // Restore Items
        if (input.restoreItems && backup.data.items) {
          for (const item of backup.data.items) {
            try {
              await ctx.databaseUnprotected.item.upsert({
                where: { id: item.id },
                update: {
                  name: item.name,
                  description: item.description,
                  category: item.category,
                  price: item.price,
                  sku: item.sku,
                  quantity: item.quantity,
                  origin: item.origin,
                  imageUrl: item.imageUrl,
                  branchId: item.branchId,
                  deleted: item.deleted || false,
                  minimumStockLevel: item.minimumStockLevel || 0,
                  minimumSellPrice: item.minimumSellPrice || 0,
                  dateUpdated: new Date()
                },
                create: {
                  id: item.id,
                  name: item.name,
                  description: item.description,
                  category: item.category,
                  price: item.price,
                  sku: item.sku,
                  quantity: item.quantity,
                  origin: item.origin,
                  imageUrl: item.imageUrl,
                  branchId: item.branchId,
                  deleted: item.deleted || false,
                  minimumStockLevel: item.minimumStockLevel || 0,
                  minimumSellPrice: item.minimumSellPrice || 0,
                  dateCreated: new Date(item.dateCreated),
                  dateUpdated: new Date()
                }
              })
              results.restored.items++
            } catch (error) {
              results.errors.push(`Item ${item.name}: ${error.message}`)
            }
          }
        }

        // Restore Sales
        if (input.restoreSales && backup.data.sales) {
          for (const sale of backup.data.sales) {
            try {
              const createdSale = await ctx.databaseUnprotected.sale.upsert({
                where: { id: sale.id },
                update: {
                  totalAmount: sale.totalAmount,
                  customerId: sale.customerId,
                  userId: sale.userId,
                  dateUpdated: new Date()
                },
                create: {
                  id: sale.id,
                  totalAmount: sale.totalAmount,
                  customerId: sale.customerId,
                  userId: sale.userId,
                  dateCreated: new Date(sale.dateCreated),
                  dateUpdated: new Date()
                }
              })

              // Restore sale items
              if (sale.saleItems) {
                for (const saleItem of sale.saleItems) {
                  try {
                    await ctx.databaseUnprotected.saleItem.upsert({
                      where: { id: saleItem.id },
                      update: {
                        quantity: saleItem.quantity,
                        unitPrice: saleItem.unitPrice,
                        totalPrice: saleItem.totalPrice,
                        saleId: createdSale.id,
                        itemId: saleItem.itemId
                      },
                      create: {
                        id: saleItem.id,
                        quantity: saleItem.quantity,
                        unitPrice: saleItem.unitPrice,
                        totalPrice: saleItem.totalPrice,
                        saleId: createdSale.id,
                        itemId: saleItem.itemId
                      }
                    })
                  } catch (error) {
                    results.errors.push(`Sale item ${saleItem.id}: ${error.message}`)
                  }
                }
              }
              results.restored.sales++
            } catch (error) {
              results.errors.push(`Sale ${sale.id}: ${error.message}`)
            }
          }
        }

        return {
          success: true,
          results,
          message: `Backup restored successfully. ${results.restored.branches} branches, ${results.restored.items} items, ${results.restored.customers} customers, ${results.restored.sales} sales restored.`
        }
      } catch (error) {
        console.error('Backup restoration failed:', error)
        throw new TRPCError({ 
          code: 'INTERNAL_SERVER_ERROR', 
          message: 'Failed to restore backup: ' + error.message 
        })
      }
    }),

  // Get backup history/metadata
  getBackupInfo: Trpc.procedurePrivate
    .query(async ({ ctx }) => {
      // Check if user is admin
      const user = await ctx.databaseUnprotected.user.findUnique({
        where: { id: ctx.session.user.id },
        include: { roles: true }
      })

      const isAdmin = user?.roles?.some(role => role.name === 'admin')
      if (!isAdmin) {
        throw new TRPCError({ code: 'FORBIDDEN', message: 'Only admins can access backup info' })
      }

      // Get database statistics
      const stats = {
        users: await ctx.databaseUnprotected.user.count(),
        branches: await ctx.databaseUnprotected.branch.count(),
        items: await ctx.databaseUnprotected.item.count(),
        customers: await ctx.databaseUnprotected.customer.count(),
        sales: await ctx.databaseUnprotected.sale.count(),
        inventoryLogs: await ctx.databaseUnprotected.inventoryLog.count(),
      }

      return {
        stats,
        lastBackupDate: null, // Could be stored in a separate table
        supportedVersion: '1.0.0'
      }
    })
})

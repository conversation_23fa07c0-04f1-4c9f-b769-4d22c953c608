// Test database connection script
const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔄 Testing database connection...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful!');
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Query test successful:', result);
    
    // Show database info (for PostgreSQL)
    try {
      const dbInfo = await prisma.$queryRaw`SELECT version()`;
      console.log('📊 Database version:', dbInfo);
    } catch (e) {
      console.log('ℹ️  Could not get database version (this is normal for some databases)');
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:');
    console.error('Error message:', error.message);
    console.error('Error code:', error.code);
    
    // Common error explanations
    if (error.message.includes('ENOTFOUND')) {
      console.log('💡 This usually means the hostname is incorrect or unreachable');
    } else if (error.message.includes('authentication failed')) {
      console.log('💡 This usually means incorrect username or password');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('💡 This usually means the database name is incorrect');
    } else if (error.message.includes('timeout')) {
      console.log('💡 This usually means network issues or firewall blocking the connection');
    }
    
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the test
testConnection()
  .then(() => {
    console.log('🏁 Connection test completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Unexpected error:', error);
    process.exit(1);
  });

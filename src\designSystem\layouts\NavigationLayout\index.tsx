'use client'

import { useUserContext } from '@/core/context'
import { useDesignSystem } from '@/designSystem/provider'
import {
    DollarOutlined,
    HistoryOutlined,
    HomeOutlined,
    ShopOutlined,
    ShoppingOutlined,
    TeamOutlined
} from '@ant-design/icons'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { Leftbar } from './components/Leftbar'
import { Logo } from './components/Logo'
import { Topbar } from './components/Topbar/index.layout'

interface Props {
  children: React.ReactNode
}

export const NavigationLayout: React.FC<Props> = ({ children }) => {
  const { checkRole, authenticationStatus } = useUserContext()
  const isAdmin = checkRole('admin')
  const router = useRouter()
  const goTo = (path: string) => router.push(path)
  const { isMobile } = useDesignSystem()

  // Sidebar open/close state - start collapsed by default for better content visibility
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const handleSidebarToggle = () => setSidebarOpen((open) => !open)

  // Base navigation items for all users
  let itemsLeftbar = [
    {
      key: '/home',
      label: 'Home',
      icon: <HomeOutlined />,
      onClick: () => goTo('/home'),
    },
    {
      key: '/items-management',
      label: 'Items Management',
      icon: <ShoppingOutlined />,
      onClick: () => goTo('/items-management'),
    },
    {
      key: '/inventory-log',
      label: 'Inventory Log',
      icon: <HistoryOutlined />,
      onClick: () => goTo('/inventory-log'),
    },
    {
      key: '/stock-management',
      label: 'Sales Management',
      icon: <DollarOutlined />,
      onClick: () => goTo('/stock-management'),
    },
    {
      key: '/branch-management',
      label: 'Branch Management',
      icon: <ShopOutlined />,
      onClick: () => goTo('/branch-management'),
    },
  ]

  // Add admin-only pages
  if (isAdmin) {
    itemsLeftbar.push({
      key: '/admin-management',
      label: 'Admin Management',
      icon: <TeamOutlined />,
      onClick: () => goTo('/admin-management'),
    })
  }

  let itemsTopbar = []
  let itemsMobile = [
    ...itemsTopbar,
    ...itemsLeftbar,
  ]

  const isLeftbar = itemsLeftbar.length > 0
  const sidebarWidth = sidebarOpen ? 250 : 80

  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'row',
        width: '100%',
        height: '100%',
        minHeight: '100vh',
        position: 'relative',
      }}
    >
      {/* Sidebar: show as overlay on mobile, fixed on desktop */}
      {isLeftbar && (
        <div
          style={{
            position: 'fixed',
            left: 0,
            top: 0,
            height: '100vh',
            zIndex: 2000,
            width: isMobile ? (sidebarOpen ? 250 : 0) : sidebarWidth,
            background: '#fff',
            boxShadow: '2px 0 8px rgba(0,0,0,0.15)',
            transition: 'width 0.3s ease, transform 0.3s ease',
            transform: isMobile && !sidebarOpen ? 'translateX(-100%)' : 'none',
            overflow: 'hidden',
          }}
        >
          <Leftbar
            items={itemsLeftbar}
            logo={null}
            collapsible={!isMobile}
            collapsed={!sidebarOpen && !isMobile}
            onClose={isMobile ? handleSidebarToggle : undefined}
          />
        </div>
      )}
      {/* Overlay for mobile when sidebar is open */}
      {isMobile && sidebarOpen && (
        <div
          onClick={handleSidebarToggle}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            background: 'rgba(0,0,0,0.3)',
            zIndex: 1999,
          }}
        />
      )}
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          width: '100%',
          height: '100%',
          minHeight: '100vh',
          marginLeft: !isMobile && isLeftbar ? `${sidebarWidth}px` : '0',
          transition: 'margin-left 0.3s ease',
        }}
      >
        <div style={{
          position: 'fixed',
          top: 0,
          right: 0,
          width: !isMobile && isLeftbar ? `calc(100% - ${sidebarWidth}px)` : '100%',
          zIndex: 1000,
          transition: 'width 0.3s ease',
        }}>
          <Topbar
            logo={!isLeftbar && <Logo className="m-2" />}
            itemsMobile={itemsMobile}
            isMobile={isMobile}
            isLoggedIn={authenticationStatus === 'authenticated'}
            items={itemsTopbar}
            onSidebarToggle={handleSidebarToggle}
            sidebarOpen={sidebarOpen}
          />
        </div>
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            width: '100%',
            height: '100%',
            padding: '20px',
            marginTop: '64px',
            overflowY: 'auto',
          }}
        >
          {children}
        </div>
      </div>
    </div>
  )
}

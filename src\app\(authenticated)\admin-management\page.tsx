'use client'

import { useUserContext } from '@/core/context'
import { Api } from '@/core/trpc'
import { PageLayout } from '@/designSystem/layouts/Page.layout'
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  DollarOutlined,
  DownloadOutlined,
  EditOutlined,
  GiftOutlined,
  MailOutlined,
  ReloadOutlined,
  TeamOutlined,
  UserAddOutlined
} from '@ant-design/icons'
import {
  Alert,
  Button,
  Card,
  Col,
  DatePicker,
  Empty,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Space,
  Statistic,
  Table,
  Tabs,
  Tag,
  Typography
} from 'antd'
import dayjs from 'dayjs'
import { useSnackbar } from 'notistack'
import { useEffect, useState } from 'react'
const { Title, Text } = Typography
const { Option } = Select
const { RangePicker } = DatePicker

export default function AdminManagementPage() {
  const { checkRole } = useUserContext()
  const isAdmin = checkRole('admin')
  const { enqueueSnackbar } = useSnackbar()

  // Admin invitation states
  const [isInviteModalVisible, setIsInviteModalVisible] = useState(false)
  const [inviteForm] = Form.useForm()

  // Sales tracking states
  const [activeTab, setActiveTab] = useState('user-invitations')
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null] | null>(null)
  const [selectedUser, setSelectedUser] = useState<string | null>(null)

  // Loyalty program states
  const [editPointsForm] = Form.useForm();
  const [isEditPointsModalVisible, setIsEditPointsModalVisible] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);

  // Responsive design state
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // API mutations and queries
  const { mutateAsync: inviteUser, isLoading: isInviting } = Api.authentication.inviteUser.useMutation()
  const { data: invitations, refetch: refetchInvitations, isLoading: isLoadingInvitations } = Api.authentication.listUserInvitations.useQuery()
  const { mutateAsync: deleteInvitation, isLoading: isDeleting } = Api.authentication.deleteUserInvitation.useMutation()
  const { mutateAsync: resendInvitation, isLoading: isResending } = Api.authentication.resendUserInvitation.useMutation()

  // Handle window resize for responsive design
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    // Check on initial load
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener('resize', checkScreenSize);

    // Clean up
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Get current admins for display
  const { data: users, isLoading: isLoadingUsers } = Api.user.findMany.useQuery({
    include: { admins: true, roles: true },
    where: { roles: { some: { name: 'admin' } } }
  })

  // Sales data query for analytics
  const {
    data: salesData,
    isLoading: isLoadingSales,
  } = Api.sale.findMany.useQuery({
    orderBy: { saleDate: 'desc' },
    select: {
      id: true,
      saleDate: true,
      userName: true,
      itemName: true,
      branchName: true,
      quantitySold: true,
      sellPrice: true,
      profit: true,
      userId: true,
      customerId: true,
      loyaltyPointsEarned: true,
    }
  })

  // Customer data for loyalty program
  const { data: customers, refetch: refetchCustomers } = Api.customer.findMany.useQuery({})
  const { mutateAsync: updateCustomer } = Api.customer.update.useMutation()

  // Handler for inviting user
  const handleInviteUser = async (values: { email: string; userType: 'user' | 'admin' }) => {
    try {
      await inviteUser({ email: values.email, userType: values.userType })
      enqueueSnackbar('Invitation sent successfully!', { variant: 'success' })
      setIsInviteModalVisible(false)
      inviteForm.resetFields()
      refetchInvitations()
    } catch (error: any) {
      enqueueSnackbar(error?.message || 'Failed to send invitation.', { variant: 'error' })
    }
  }

  // Handler for deleting invitation
  const handleDeleteInvitation = async (id: string) => {
    try {
      await deleteInvitation({ id })
      enqueueSnackbar('Invitation deleted.', { variant: 'success' })
      refetchInvitations()
    } catch (error: any) {
      enqueueSnackbar(error?.message || 'Failed to delete invitation.', { variant: 'error' })
    }
  }

  // Handler for resending invitation
  const handleResendInvitation = async (id: string) => {
    try {
      await resendInvitation({ id })
      enqueueSnackbar('Invitation resent.', { variant: 'success' })
    } catch (error: any) {
      enqueueSnackbar(error?.message || 'Failed to resend invitation.', { variant: 'error' })
    }
  }

  // Define helper functions for sales tracking
  const getFilteredSales = () => {
    if (!salesData) return [];

    let filtered = salesData;

    // Filter by user
    if (selectedUser) {
      filtered = filtered.filter((sale: any) => sale.userId === selectedUser);
    }

    // Filter by date range
    if (dateRange && dateRange[0] && dateRange[1]) {
      const startDate = dateRange[0].startOf('day');
      const endDate = dateRange[1].endOf('day');

      filtered = filtered.filter((sale: any) => {
        const saleDate = dayjs(sale.saleDate);
        return saleDate.isAfter(startDate) && saleDate.isBefore(endDate);
      });
    }

    return filtered;
  };

  const calculateTotalRevenue = () => {
    return getFilteredSales().reduce((total: number, sale: any) => total + (sale.sellPrice * sale.quantitySold), 0);
  };

  const calculateTotalProfit = () => {
    return getFilteredSales().reduce((total: number, sale: any) => total + sale.profit, 0);
  };

  // Define sales columns with responsive design
  const salesColumns = [
    {
      title: 'Date',
      dataIndex: 'saleDate',
      key: 'saleDate',
      render: (date: string) => dayjs(date).format(isMobile ? 'DD/MM/YY' : 'DD-MM-YYYY HH:mm'),
      width: isMobile ? 80 : 150,
      ...(isMobile && { fixed: 'left' as const }),
    },
    {
      title: 'User',
      dataIndex: 'userName',
      key: 'userName',
      ellipsis: true,
      width: isMobile ? 100 : 120,
      responsive: isMobile ? [] : ['sm' as const],
    },
    {
      title: 'Item',
      dataIndex: 'itemName',
      key: 'itemName',
      ellipsis: true,
      width: isMobile ? 120 : 150,
    },
    {
      title: 'Branch',
      dataIndex: 'branchName',
      key: 'branchName',
      ellipsis: true,
      width: 100,
      responsive: ['md' as const],
    },
    {
      title: 'Qty',
      dataIndex: 'quantitySold',
      key: 'quantitySold',
      width: isMobile ? 50 : 80,
      align: 'center' as const,
    },
    {
      title: 'Price',
      dataIndex: 'sellPrice',
      key: 'sellPrice',
      render: (price: number) => price ? `${isMobile ? '' : 'KES '}${price.toFixed(0)}` : 'N/A',
      width: isMobile ? 70 : 100,
      align: 'right' as const,
    },
    {
      title: 'Total',
      key: 'total',
      render: (_: any, record: any) => record.sellPrice && record.quantitySold ?
        `${isMobile ? '' : 'KES '}${(record.sellPrice * record.quantitySold).toFixed(0)}` : 'N/A',
      width: isMobile ? 80 : 120,
      align: 'right' as const,
      ...(isMobile && { fixed: 'right' as const }),
    },
    {
      title: 'Profit',
      dataIndex: 'profit',
      key: 'profit',
      render: (profit: number) => profit ?
        <Tag color="green" style={{ fontSize: isMobile ? '10px' : '12px' }}>
          {`${isMobile ? '' : 'KES '}${profit.toFixed(0)}`}
        </Tag> :
        <Tag color="gray" style={{ fontSize: isMobile ? '10px' : '12px' }}>N/A</Tag>,
      width: isMobile ? 70 : 100,
      align: 'center' as const,
      responsive: ['sm' as const],
    },
  ];

  // Add function to handle CSV export
  const handleExportCSV = () => {
    const filteredSales = getFilteredSales();
    if (filteredSales.length === 0) {
      enqueueSnackbar('No data to export', { variant: 'warning' });
      return;
    }

    // Create CSV content
    const headers = [
      'Date',
      'User',
      'Item',
      'Branch',
      'Quantity',
      'Price (KES)',
      'Total (KES)',
      'Profit (KES)'
    ];

    const csvContent = [
      headers.join(','),
      ...filteredSales.map(sale => [
        dayjs(sale.saleDate).format('DD-MM-YYYY HH:mm'),
        sale.userName,
        sale.itemName,
        sale.branchName,
        sale.quantitySold,
        sale.sellPrice.toFixed(2),
        (sale.sellPrice * sale.quantitySold).toFixed(2),
        sale.profit.toFixed(2)
      ].join(','))
    ].join('\n');

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `sales_report_${dayjs().format('YYYY-MM-DD_HH-mm')}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleUpdateLoyaltyPoints = async (values: any) => {
    try {
      await updateCustomer({
        where: { id: selectedCustomer.id },
        data: { loyaltyPoints: values.loyaltyPoints }
      })
      enqueueSnackbar('Loyalty points updated successfully', { variant: 'success' })
      refetchCustomers()
      setIsEditPointsModalVisible(false)
      editPointsForm.resetFields()
    } catch (error) {
      enqueueSnackbar('Failed to update loyalty points', { variant: 'error' })
    }
  }


  return (
    <PageLayout layout="full-width">
      {/* Header Section */}
      <div style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '16px',
        padding: isMobile ? '24px 16px' : '32px 24px',
        marginBottom: '24px',
        color: 'white'
      }}>
        <Row justify="space-between" align="middle" wrap={isMobile}>
          <Col xs={24} sm={16}>
            <Space direction="vertical" size="small">
              <Title level={1} style={{ color: 'white', margin: 0, fontSize: isMobile ? '24px' : '32px' }}>
                <TeamOutlined style={{ marginRight: '12px' }} />
                Admin Management
              </Title>
              <Text style={{ color: 'rgba(255,255,255,0.9)', fontSize: isMobile ? '14px' : '16px' }}>
                Manage administrator invitations and system access
              </Text>
            </Space>
          </Col>
          <Col xs={24} sm={8} style={{ textAlign: isMobile ? 'left' : 'right', marginTop: isMobile ? '16px' : '0' }}>
            <Button
              type="primary"
              size="large"
              icon={<UserAddOutlined />}
              onClick={() => setIsInviteModalVisible(true)}
              style={{
                background: 'rgba(255,255,255,0.2)',
                border: '1px solid rgba(255,255,255,0.3)',
                backdropFilter: 'blur(10px)',
                width: isMobile ? '100%' : 'auto'
              }}
            >
              Invite Admin
            </Button>
          </Col>
        </Row>
      </div>

      {/* Admin Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Current Admins"
              value={users?.length || 0}
              prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Pending Invitations"
              value={invitations?.length || 0}
              prefix={<ClockCircleOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Sales"
              value={salesData?.length || 0}
              prefix={<DollarOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Customers"
              value={customers?.length || 0}
              prefix={<GiftOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Invite User Modal */}
      <Modal
        title={
          <Space>
            <MailOutlined style={{ color: '#1890ff' }} />
            <span>Invite New User</span>
          </Space>
        }
        open={isInviteModalVisible}
        onCancel={() => setIsInviteModalVisible(false)}
        footer={null}
        width={isMobile ? '95vw' : isTablet ? '80vw' : 520}
        style={{
          top: isMobile ? 10 : isTablet ? 50 : 100,
          maxWidth: '95vw',
          margin: isMobile ? '10px' : 'auto'
        }}
        styles={{
          body: {
            maxHeight: isMobile ? '80vh' : '70vh',
            overflowY: 'auto',
            padding: isMobile ? '16px' : '24px'
          }
        }}
        centered={isMobile}
      >
        <Form form={inviteForm} layout="vertical" onFinish={handleInviteUser} initialValues={{ userType: 'user' }}>
          <Form.Item
            label="User Type"
            name="userType"
            rules={[{ required: true, message: 'Please select a user type.' }]}
          >
            <Select size="large" placeholder="Select user type">
              <Option value="user">Regular User</Option>
              <Option value="admin">Administrator</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Email Address"
            name="email"
            rules={[
              { required: true, message: 'Please enter an email address.' },
              { type: 'email', message: 'Please enter a valid email address.' }
            ]}
          >
            <Input
              prefix={<MailOutlined />}
              placeholder="<EMAIL>"
              disabled={isInviting}
              size="large"
            />
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={isInviting}
              block
              size="large"
              icon={<UserAddOutlined />}
            >
              Send Invitation
            </Button>
          </Form.Item>
        </Form>
      </Modal>
      {/* Only show content to admin users */}
      {isAdmin ? (
        <Tabs
          defaultActiveKey="user-invitations"
          onChange={setActiveTab}
          items={[
            {
              key: 'user-invitations',
              label: <span><UserAddOutlined /> User Invitations</span>,
              children: (
                <>
                  {/* Pending Invitations Section */}
                  <Card
                    title={
                      <Space>
                        <ClockCircleOutlined style={{ color: '#faad14' }} />
                        <span>Pending User Invitations</span>
                      </Space>
                    }
                    style={{ marginBottom: 24 }}
                    loading={isLoadingInvitations}
                    extra={
                      <Button
                        type="primary"
                        icon={<UserAddOutlined />}
                        onClick={() => setIsInviteModalVisible(true)}
                        size={isMobile ? 'small' : 'middle'}
                      >
                        {isMobile ? 'Invite' : 'Invite User'}
                      </Button>
                    }
                  >
                    {(!invitations || invitations.length === 0) ? (
                      <Empty
                        description="No pending invitations"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    ) : (
                      <Table
                        dataSource={invitations}
                        rowKey="id"
                        pagination={false}
                        scroll={{ x: isMobile ? 400 : undefined }}
                        size={isMobile ? 'small' : 'middle'}
                        columns={[
                          {
                            title: 'Email',
                            dataIndex: 'email',
                            key: 'email',
                            ellipsis: isMobile,
                            render: (email) => (
                              <Space>
                                <MailOutlined style={{ color: '#1890ff' }} />
                                <span>{email}</span>
                              </Space>
                            )
                          },
                          {
                            title: 'Type',
                            dataIndex: 'userType',
                            key: 'userType',
                            render: (userType: string) => (
                              <Tag color={userType === 'admin' ? 'red' : 'blue'}>
                                {userType === 'admin' ? 'Administrator' : 'User'}
                              </Tag>
                            ),
                            responsive: isMobile ? [] : ['md']
                          },
                          {
                            title: 'Sent',
                            dataIndex: 'createdAt',
                            key: 'createdAt',
                            render: (date: string) => (
                              <Space>
                                <ClockCircleOutlined style={{ color: '#faad14' }} />
                                <span>{dayjs(date).format(isMobile ? 'MM/DD/YY' : 'MMM DD, YYYY HH:mm')}</span>
                              </Space>
                            ),
                            responsive: isMobile ? [] : ['sm']
                          },
                          {
                            title: 'Actions',
                            key: 'actions',
                            render: (_: any, record: any) => (
                              <Space size="small" wrap={isMobile}>
                                <Button
                                  size="small"
                                  icon={<ReloadOutlined />}
                                  loading={isResending}
                                  onClick={() => handleResendInvitation(record.id)}
                                  title="Resend invitation"
                                >
                                  {isMobile ? '' : 'Resend'}
                                </Button>
                                <Button
                                  size="small"
                                  danger
                                  icon={<DeleteOutlined />}
                                  loading={isDeleting}
                                  onClick={() => handleDeleteInvitation(record.id)}
                                  title="Delete invitation"
                                >
                                  {isMobile ? '' : 'Delete'}
                                </Button>
                              </Space>
                            ),
                          },
                        ]}
                      />
                    )}
                  </Card>

                  {/* Current Admins Section */}
                  <Card
                    title={
                      <Space>
                        <TeamOutlined style={{ color: '#52c41a' }} />
                        <span>Current Administrators</span>
                      </Space>
                    }
                    loading={isLoadingUsers}
                  >
                    {(!users || users.length === 0) ? (
                      <Empty
                        description="No administrators found"
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                      />
                    ) : (
                      <Table
                        dataSource={users}
                        rowKey="id"
                        pagination={{
                          pageSize: 10,
                          size: isMobile ? 'small' : undefined
                        }}
                        scroll={{ x: isMobile ? 400 : undefined }}
                        size={isMobile ? 'small' : 'middle'}
                        columns={[
                          {
                            title: 'Email',
                            dataIndex: 'email',
                            key: 'email',
                            ellipsis: isMobile,
                            render: (email) => (
                              <Space>
                                <MailOutlined style={{ color: '#1890ff' }} />
                                <span>{email}</span>
                              </Space>
                            )
                          },
                          {
                            title: 'Name',
                            dataIndex: 'name',
                            key: 'name',
                            ellipsis: isMobile
                          },
                          {
                            title: 'Status',
                            key: 'status',
                            render: () => (
                              <Space>
                                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                <span style={{ color: '#52c41a' }}>Active</span>
                              </Space>
                            ),
                            responsive: isMobile ? [] : ['md']
                          },
                          {
                            title: 'Last Updated',
                            dataIndex: 'dateUpdated',
                            key: 'dateUpdated',
                            render: text => dayjs(text).format(isMobile ? 'MM/DD/YY' : 'MMM DD, YYYY'),
                            responsive: isMobile ? [] : ['lg']
                          }
                        ]}
                      />
                    )}
                  </Card>
                </>
              )
            },
            {
              key: 'sales',
              label: <span><DollarOutlined /> Sales by User</span>,
              children: (
                <>
                  <Row gutter={[16, 16]} style={{ marginTop: '20px' }}>
                    <Col span={24}>
                      <Card>
                        <Space direction="vertical" style={{ width: '100%' }}>
                          <div style={{
                            display: 'flex',
                            flexDirection: isMobile ? 'column' : 'row',
                            justifyContent: 'space-between',
                            alignItems: isMobile ? 'stretch' : 'center',
                            gap: isMobile ? '16px' : '10px',
                            marginBottom: '16px'
                          }}>
                            <Title level={4} style={{ margin: 0 }}>Filter Sales</Title>
                            <Button
                              type="primary"
                              icon={<DownloadOutlined />}
                              onClick={handleExportCSV}
                              disabled={!salesData || salesData.length === 0}
                              style={{
                                width: isMobile ? '100%' : 'auto',
                                minWidth: isMobile ? 'auto' : '140px'
                              }}
                              size={isMobile ? 'large' : 'middle'}
                            >
                              {isMobile ? 'Export CSV' : 'Export to CSV'}
                            </Button>
                          </div>
                          <div style={{
                            display: 'grid',
                            gridTemplateColumns: isMobile ? '1fr' : isTablet ? '1fr 1fr' : '200px 300px',
                            gap: '16px',
                            width: '100%'
                          }}>
                            <Select
                              placeholder="Select User"
                              style={{ width: '100%' }}
                              allowClear
                              onChange={(value) => setSelectedUser(value)}
                              size={isMobile ? 'large' : 'middle'}
                            >
                              {users?.map(user => (
                                <Option key={user.id} value={user.id}>
                                  {user.name || user.email}
                                </Option>
                              ))}
                            </Select>
                            <RangePicker
                              onChange={(dates) => setDateRange(dates)}
                              style={{ width: '100%' }}
                              size={isMobile ? 'large' : 'middle'}
                              placeholder={['Start Date', 'End Date']}
                            />
                          </div>
                        </Space>
                      </Card>
                    </Col>
                  </Row>

                  <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
                    <Col xs={24} sm={8} md={8}>
                      <Card style={{ height: '100%' }}>
                        <Statistic
                          title="Total Sales"
                          value={getFilteredSales().length}
                          suffix={isMobile ? '' : 'transactions'}
                          valueStyle={{
                            fontSize: isMobile ? '18px' : '24px',
                            color: '#1890ff'
                          }}
                        />
                      </Card>
                    </Col>
                    <Col xs={24} sm={8} md={8}>
                      <Card style={{ height: '100%' }}>
                        <Statistic
                          title={isMobile ? 'Revenue' : 'Total Revenue'}
                          value={calculateTotalRevenue()}
                          prefix="KES "
                          precision={isMobile ? 0 : 2}
                          valueStyle={{
                            fontSize: isMobile ? '16px' : '24px',
                            wordBreak: 'break-word',
                            color: '#52c41a'
                          }}
                        />
                      </Card>
                    </Col>
                    <Col xs={24} sm={8} md={8}>
                      <Card style={{ height: '100%' }}>
                        <Statistic
                          title={isMobile ? 'Profit' : 'Total Profit'}
                          value={calculateTotalProfit()}
                          prefix="KES "
                          precision={isMobile ? 0 : 2}
                          valueStyle={{
                            fontSize: isMobile ? '16px' : '24px',
                            wordBreak: 'break-word',
                            color: '#722ed1'
                          }}
                        />
                      </Card>
                    </Col>
                  </Row>

                  <div style={{
                    width: '100%',
                    overflowX: 'auto',
                    marginTop: '16px',
                    border: isMobile ? '1px solid #f0f0f0' : 'none',
                    borderRadius: isMobile ? '8px' : '0'
                  }}>
                    <Table
                      dataSource={getFilteredSales()}
                      columns={salesColumns}
                      loading={isLoadingSales}
                      rowKey="id"
                      pagination={{
                        pageSize: isMobile ? 5 : 10,
                        size: isMobile ? 'small' : undefined,
                        showSizeChanger: !isMobile,
                        showQuickJumper: !isMobile,
                        showTotal: (total, range) =>
                          isMobile ? `${range[0]}-${range[1]} of ${total}` :
                          `${range[0]}-${range[1]} of ${total} items`
                      }}
                      scroll={{
                        x: isMobile ? 600 : 'max-content',
                        y: isMobile ? 400 : undefined
                      }}
                      size={isMobile ? 'small' : 'middle'}
                      sticky={!isMobile}
                    />
                  </div>
                </>
              )
            },
            {
              key: 'loyalty',
              label: <span><GiftOutlined /> Loyalty Program</span>,
              children: (
                <>
                  <Row gutter={[16, 16]} style={{ marginTop: '20px' }}>
                    <Col span={24}>
                      <Card>
                        <Title level={4}>Customer Loyalty Program</Title>
                        <Alert
                          message="Loyalty Program Rules"
                          description="Customers earn 5% of profit as loyalty points. Referrers earn 2% of profit when their referrals make purchases."
                          type="info"
                          showIcon
                          style={{ marginBottom: '20px' }}
                        />
                        <div style={{ width: '100%', overflowX: 'auto' }}>
                          <Table
                            dataSource={customers}
                            columns={[
                              {
                                title: 'Customer Name',
                                dataIndex: 'name',
                                key: 'name',
                                ellipsis: isMobile
                              },
                              {
                                title: 'Phone Number',
                                dataIndex: 'phoneNumber',
                                key: 'phoneNumber',
                                responsive: ['sm']
                              },
                              {
                                title: 'Loyalty Points',
                                dataIndex: 'loyaltyPoints',
                                key: 'loyaltyPoints',
                                render: (points: number) => `KES ${points.toFixed(2)}`,
                              },
                              {
                                title: 'Referred By',
                                key: 'referredBy',
                                render: (_, record) => {
                                  const referrer = customers?.find(c => c.id === record.referredBy)
                                  return referrer ? referrer.name : '-'
                                },
                                responsive: ['md']
                              },
                              {
                                title: 'Referrals',
                                key: 'referrals',
                                render: (_, record) => {
                                  return customers?.filter(c => c.referredBy === record.id).length || 0
                                },
                                responsive: ['md']
                              },
                              {
                                title: 'Total Earned',
                                key: 'totalPoints',
                                render: (_, record) => {
                                  const sales = salesData?.filter(s => s.customerId === record.id) || [];
                                  const pointsEarned = sales.reduce((sum, sale) => sum + sale.loyaltyPointsEarned, 0);
                                  return `KES ${pointsEarned.toFixed(2)}`;
                                },
                                responsive: ['lg']
                              },
                              {
                                title: 'Actions',
                                key: 'actions',
                                render: (_, record) => (
                                  <Button
                                    type="primary"
                                    icon={<EditOutlined />}
                                    size={isMobile ? 'small' : 'middle'}
                                    onClick={() => {
                                      setSelectedCustomer(record);
                                      editPointsForm.setFieldsValue({
                                        loyaltyPoints: record.loyaltyPoints
                                      });
                                      setIsEditPointsModalVisible(true);
                                    }}
                                  >
                                    {isMobile ? '' : 'Edit Points'}
                                  </Button>
                                ),
                              },
                            ]}
                            rowKey="id"
                            pagination={{
                              pageSize: 10,
                              size: isMobile ? 'small' : undefined
                            }}
                            scroll={{ x: isMobile ? 600 : undefined }}
                            size={isMobile ? 'small' : 'middle'}
                          />
                        </div>
                      </Card>
                    </Col>
                  </Row>

                  <Modal
                    title="Edit Loyalty Points"
                    open={isEditPointsModalVisible}
                    onCancel={() => {
                      setIsEditPointsModalVisible(false);
                      editPointsForm.resetFields();
                    }}
                    footer={null}
                    width={isMobile ? '95vw' : isTablet ? '80vw' : 520}
                    style={{
                      top: isMobile ? 10 : isTablet ? 50 : 100,
                      maxWidth: '95vw',
                      margin: isMobile ? '10px' : 'auto'
                    }}
                    styles={{
                      body: {
                        maxHeight: isMobile ? '80vh' : '70vh',
                        overflowY: 'auto',
                        padding: isMobile ? '16px' : '24px'
                      }
                    }}
                    centered={isMobile}
                  >
                    <Form
                      form={editPointsForm}
                      onFinish={handleUpdateLoyaltyPoints}
                      layout="vertical"
                    >
                      <Form.Item
                        name="loyaltyPoints"
                        label="Loyalty Points"
                        rules={[{ required: true, message: 'Please input the loyalty points!' }]}
                      >
                        <InputNumber
                          style={{ width: '100%' }}
                          formatter={value => `KES ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                          parser={value => value!.replace(/KES\s?|(,*)/g, '')}
                          placeholder="Enter loyalty points"
                        />
                      </Form.Item>
                      <Form.Item>
                        <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
                          <Button onClick={() => {
                            setIsEditPointsModalVisible(false);
                            editPointsForm.resetFields();
                          }}>
                            Cancel
                          </Button>
                          <Button type="primary" htmlType="submit">
                            Update Points
                          </Button>
                        </Space>
                      </Form.Item>
                    </Form>
                  </Modal>
                </>
              ),
            }
          ]}
        />
      ) : (
        <div style={{ marginTop: '20px' }}>
          <Alert
            message="Access Restricted"
            description="You need administrator privileges to access this page."
            type="warning"
            showIcon
          />
        </div>
      )}


    </PageLayout>
  )
}
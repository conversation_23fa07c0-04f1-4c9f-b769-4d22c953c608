{"name": "@marblism/nextjs", "version": "1.0.2", "private": true, "type": "module", "scripts": {"init": "pnpm install && pnpm run docker:init ; pnpm run env:init ; pnpm run global:sync ; pnpm run database:seed", "dev": "pnpm run global:sync && next dev -p 8099", "build": "pnpm run crud:sync && next build", "start": "next start -p 8099", "lint": "next lint", "check": "tsc", "global:sync": "pnpm run crud:sync && pnpm run database:sync:dev", "crud:sync": "pnpm zenstack generate", "database:sync": "prisma db push", "database:sync:dev": "prisma db push --accept-data-loss", "database:seed": "tsx ./prisma/seed/mockUser.ts && tsx ./prisma/seed/mockData.ts ", "database:seed:salon": "tsx ./prisma/seed/seedSalonItems.ts", "database:backup": "node scripts/backup.js backup", "database:restore": "node scripts/backup.js restore", "database:migration:generate": "prisma migrate dev --name auto --create-only", "database:migration:run": "prisma migrate deploy", "database:studio": "prisma studio", "docker:init": "docker compose -p marblism-app up -d", "env:init": "node scripts/env.js", "format": "pnpm run format:code && pnpm run format:models", "format:code": "prettier --write \"src/!(*.marblism)/**/*.{ts,tsx}\"", "format:models": "node scripts/format.models.js", "postbuild": "prisma migrate deploy"}, "dependencies": {"@ant-design/icons": "^5.3.7", "@auth/prisma-adapter": "1.6.0", "@aws-sdk/client-s3": "^3.502.0", "@aws-sdk/s3-request-presigner": "^3.572.0", "@mapbox/mapbox-sdk": "^0.16.0", "@prisma/client": "5.15.1", "@react-pdf/renderer": "^4.3.0", "@tailwindcss/typography": "^0.5.13", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.37.1", "@trpc/react-query": "^10.37.1", "@trpc/server": "^10.45.2", "@upstash/redis": "^1.34.5", "antd": "^5.18.2", "autoprefixer": "^10.4.19", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "dayjs": "^1.11.11", "framer-motion": "^11.3.7", "geist": "^1.3.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.0", "mapbox-gl": "^3.5.1", "multer": "1.4.5-lts.1", "next": "^14.2.1", "next-auth": "^4.24.7", "node-mailjet": "^6.0.5", "nodemailer": "^6.9.14", "notistack": "^3.0.1", "openai": "^4.52.0", "postcss": "^8.4.39", "prisma": "5.15.1", "react": "^18.3.0", "react-dom": "^18.3.0", "react-error-boundary": "^5.0.0", "react-to-print": "^3.0.5", "recharts": "^2.15.1", "resend": "^4.1.2", "sass": "^1.62.1", "server-only": "^0.0.1", "stripe": "^15.12.0", "superjson": "^2.2.1", "tailwind-merge": "^2.4.0", "tailwindcss": "^3.4.6", "ts-node": "^10.9.2", "tsx": "^4.15.6", "uuid": "^10.0.0", "xlsx": "^0.18.5", "zod": "^3.21.1", "zod-form-data": "^2.0.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/eslint": "^8.56.2", "@types/mapbox__mapbox-sdk": "^0.14.0", "@types/node": "^20.11.20", "@types/react": "^18.2.57", "@types/react-dom": "^18.2.19", "@zenstackhq/runtime": "2.2.4", "@zenstackhq/server": "2.2.4", "@zenstackhq/tanstack-query": "2.2.4", "@zenstackhq/trpc": "2.2.4", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-next": "^14.1.3", "nodemon": "^3.1.3", "prettier": "^3.2.5", "prompt-sync": "^4.2.0", "typescript": "^5.4.2", "zenstack": "2.2.4"}, "ct3aMetadata": {"initVersion": "7.34.0"}, "zenstack": {"schema": "./models/schema.zmodel"}, "packageManager": "pnpm@10.5.2+sha512.da9dc28cd3ff40d0592188235ab25d3202add8a207afbedc682220e4a0029ffbff4562102b9e6e46b4e3f9e8bd53e6d05de48544b0c57d4b0179e22c76d1199b"}
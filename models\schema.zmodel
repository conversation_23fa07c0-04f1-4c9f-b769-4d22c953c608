
import "./account.zmodel";
import "./user.zmodel";
import "./role.zmodel";
import "./session.zmodel";

import "./admin.zmodel";
import "./branch.zmodel";
import "./customer.zmodel";

// Import models in the correct order to avoid circular references
import "./item.zmodel";
import "./sale.zmodel";
import "./saleItem.zmodel";

import "./stockTransfer.zmodel";
import "./inventoryLog.zmodel";

generator client {
    provider = "prisma-client-js"
}

plugin prisma {
    provider = '@core/prisma'
    output = "./../prisma/schema.prisma"
}

datasource db {
    provider = "postgresql"
    url      = env("SERVER_DATABASE_URL")
}

plugin trpc {
    provider = '@zenstackhq/trpc'
    output = "./../src/.marblism/api"
    generateClientHelpers = ['react']
    generateModelActions = ['create', 'update', 'delete', 'findMany', 'findFirst', 'findUnique']
}

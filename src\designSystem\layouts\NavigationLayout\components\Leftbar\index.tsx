import {
    CloseOutlined
} from '@ant-design/icons'
import { But<PERSON>, <PERSON>u, Row } from 'antd'
import Sider from 'antd/es/layout/Sider'
import { usePathname } from 'next/navigation'
import { ReactNode } from 'react'

interface Props {
  logo: ReactNode
  items: { key: string; label: string; onClick: () => void }[]
  collapsible?: boolean
  collapsed?: boolean
  onClose?: () => void
}

export const Leftbar: React.FC<Props> = ({ logo, items, collapsible, collapsed, onClose }) => {
  const pathname = usePathname()

  return (
    <Sider
      width={250}
      trigger={null}
      collapsible={collapsible}
      collapsed={collapsed}
      style={{
        height: '100vh',
        overflow: 'auto',
        background: '#fff',
        boxShadow: '2px 0 8px rgba(0,0,0,0.04)',
        position: 'relative',
      }}
    >
      <Row align="middle" style={{ height: 60, padding: '0 16px', justifyContent: 'space-between' }}>
        {logo}
        {onClose && (
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
            style={{ fontSize: 20 }}
          />
        )}
      </Row>
      <Menu
        mode="inline"
        items={items}
        selectedKeys={[pathname]}
        style={{
          width: '100%',
          height: 'calc(100% - 60px)', // Adjust for logo/close height
        }}
      />
    </Sider>
  )
}

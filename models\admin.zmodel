
 import "./user.zmodel"  

model Admin {

    id        String    @id @default(uuid())  
    userId    String      
    user      User?      @relation(fields: [userId], references: [id], name:"user")  

dateCreated DateTime @default(now())
      dateUpdated DateTime @updatedAt  @default(now())
      @@allow("all", true)
}

model AdminInvitation {
    id         String   @id @default(uuid())
    email      String   @unique
    token      String   @unique
    invitedBy  String   // User ID of the admin who sent the invite
    expiresAt  DateTime
    acceptedAt DateTime?
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt @default(now())
}
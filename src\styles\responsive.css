/* Responsive styles for the application */

/* Notification system responsive styles */
.ant-notification {
  z-index: 9999 !important;
}

.ant-notification-notice {
  word-wrap: break-word !important;
}

.ant-modal {
  max-width: 95vw !important;
}

.ant-table-wrapper {
  overflow-x: auto !important;
}

/* Mobile styles */
@media (max-width: 768px) {
  .ant-notification-notice {
    max-width: 90vw !important;
    margin: 8px !important;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }
  
  .ant-tag {
    font-size: 10px !important;
    padding: 0 4px !important;
  }
  
  .ant-btn-sm {
    font-size: 11px !important;
    padding: 0 8px !important;
    height: 28px !important;
  }
  
  .ant-space-item {
    margin-bottom: 8px !important;
  }
}

/* Extra small mobile styles */
@media (max-width: 480px) {
  .ant-table-thead > tr > th {
    padding: 6px 2px !important;
    font-size: 11px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 6px 2px !important;
    font-size: 11px !important;
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .ant-notification-notice {
    max-width: 384px !important;
    margin: 16px !important;
  }
}

/* Custom responsive utilities */
.responsive-grid-mobile {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.responsive-grid-tablet {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.responsive-grid-desktop {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.responsive-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.02);
  overflow: hidden;
}

.responsive-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.responsive-header-mobile {
  padding: 20px 16px;
}

.responsive-header-desktop {
  padding: 32px 24px;
}

/* Touch-friendly button styles */
.touch-button-mobile {
  height: 48px !important;
  font-weight: 500;
}

.touch-button-desktop {
  height: 40px !important;
  font-weight: 500;
}

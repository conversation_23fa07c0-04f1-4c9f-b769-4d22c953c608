/* eslint-disable */
import { type RouterFactory, type ProcBuilder, type BaseConfig, db } from ".";
import * as _Schema from '@zenstackhq/runtime/zod/input';
const $Schema: typeof _Schema = (_Schema as any).default ?? _Schema;
import { checkRead, checkMutate } from '../helper';
import type { Prisma } from '@prisma/client';
import type { UseTRPCMutationOptions, UseTRPCMutationResult, UseTRPCQueryOptions, UseTRPCQueryResult, UseTRPCInfiniteQueryOptions, UseTRPCInfiniteQueryResult } from '@trpc/react-query/shared';
import type { TRPCClientErrorLike } from '@trpc/client';
import type { AnyRouter } from '@trpc/server';

export default function createRouter<Config extends BaseConfig>(router: RouterFactory<Config>, procedure: ProcBuilder<Config>) {
    return router({

        create: procedure.input($Schema.AdminInvitationInputSchema.create).mutation(async ({ ctx, input }) => checkMutate(db(ctx).adminInvitation.create(input as any))),

        delete: procedure.input($Schema.AdminInvitationInputSchema.delete).mutation(async ({ ctx, input }) => checkMutate(db(ctx).adminInvitation.delete(input as any))),

        findFirst: procedure.input($Schema.AdminInvitationInputSchema.findFirst).query(({ ctx, input }) => checkRead(db(ctx).adminInvitation.findFirst(input as any))),

        findMany: procedure.input($Schema.AdminInvitationInputSchema.findMany).query(({ ctx, input }) => checkRead(db(ctx).adminInvitation.findMany(input as any))),

        findUnique: procedure.input($Schema.AdminInvitationInputSchema.findUnique).query(({ ctx, input }) => checkRead(db(ctx).adminInvitation.findUnique(input as any))),

        update: procedure.input($Schema.AdminInvitationInputSchema.update).mutation(async ({ ctx, input }) => checkMutate(db(ctx).adminInvitation.update(input as any))),

    }
    );
}

export interface ClientType<AppRouter extends AnyRouter, Context = AppRouter['_def']['_config']['$types']['ctx']> {
    create: {

        useMutation: <T extends Prisma.AdminInvitationCreateArgs>(opts?: UseTRPCMutationOptions<
            Prisma.AdminInvitationCreateArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.AdminInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.AdminInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.AdminInvitationCreateArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.AdminInvitationCreateArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.AdminInvitationGetPayload<T>, Context>) => Promise<Prisma.AdminInvitationGetPayload<T>>
            };

    };
    delete: {

        useMutation: <T extends Prisma.AdminInvitationDeleteArgs>(opts?: UseTRPCMutationOptions<
            Prisma.AdminInvitationDeleteArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.AdminInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.AdminInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.AdminInvitationDeleteArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.AdminInvitationDeleteArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.AdminInvitationGetPayload<T>, Context>) => Promise<Prisma.AdminInvitationGetPayload<T>>
            };

    };
    findFirst: {

        useQuery: <T extends Prisma.AdminInvitationFindFirstArgs, TData = Prisma.AdminInvitationGetPayload<T>>(
            input: Prisma.SelectSubset<T, Prisma.AdminInvitationFindFirstArgs>,
            opts?: UseTRPCQueryOptions<string, T, Prisma.AdminInvitationGetPayload<T>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.AdminInvitationFindFirstArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.AdminInvitationFindFirstArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Prisma.AdminInvitationGetPayload<T>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Prisma.AdminInvitationGetPayload<T>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    findMany: {

        useQuery: <T extends Prisma.AdminInvitationFindManyArgs, TData = Array<Prisma.AdminInvitationGetPayload<T>>>(
            input: Prisma.SelectSubset<T, Prisma.AdminInvitationFindManyArgs>,
            opts?: UseTRPCQueryOptions<string, T, Array<Prisma.AdminInvitationGetPayload<T>>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.AdminInvitationFindManyArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.AdminInvitationFindManyArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Array<Prisma.AdminInvitationGetPayload<T>>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Array<Prisma.AdminInvitationGetPayload<T>>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    findUnique: {

        useQuery: <T extends Prisma.AdminInvitationFindUniqueArgs, TData = Prisma.AdminInvitationGetPayload<T>>(
            input: Prisma.SelectSubset<T, Prisma.AdminInvitationFindUniqueArgs>,
            opts?: UseTRPCQueryOptions<string, T, Prisma.AdminInvitationGetPayload<T>, TData, Error>
        ) => UseTRPCQueryResult<
            TData,
            TRPCClientErrorLike<AppRouter>
        >;
        useInfiniteQuery: <T extends Prisma.AdminInvitationFindUniqueArgs>(
            input: Omit<Prisma.SelectSubset<T, Prisma.AdminInvitationFindUniqueArgs>, 'cursor'>,
            opts?: UseTRPCInfiniteQueryOptions<string, T, Prisma.AdminInvitationGetPayload<T>, Error>
        ) => UseTRPCInfiniteQueryResult<
            Prisma.AdminInvitationGetPayload<T>,
            TRPCClientErrorLike<AppRouter>
        >;

    };
    update: {

        useMutation: <T extends Prisma.AdminInvitationUpdateArgs>(opts?: UseTRPCMutationOptions<
            Prisma.AdminInvitationUpdateArgs,
            TRPCClientErrorLike<AppRouter>,
            Prisma.AdminInvitationGetPayload<T>,
            Context
        >,) =>
            Omit<UseTRPCMutationResult<Prisma.AdminInvitationGetPayload<T>, TRPCClientErrorLike<AppRouter>, Prisma.SelectSubset<T, Prisma.AdminInvitationUpdateArgs>, Context>, 'mutateAsync'> & {
                mutateAsync:
                <T extends Prisma.AdminInvitationUpdateArgs>(variables: T, opts?: UseTRPCMutationOptions<T, TRPCClientErrorLike<AppRouter>, Prisma.AdminInvitationGetPayload<T>, Context>) => Promise<Prisma.AdminInvitationGetPayload<T>>
            };

    };
}

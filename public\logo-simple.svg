<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="40" viewBox="0 0 120 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="120" height="40" rx="8" fill="url(#gradient)"/>
  
  <!-- Gradient Definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Simple Chair Icon -->
  <g transform="translate(8, 8)">
    <!-- Chair Back -->
    <rect x="2" y="2" width="12" height="16" rx="6" fill="white" opacity="0.95"/>
    
    <!-- Chair Seat -->
    <ellipse cx="8" cy="20" rx="8" ry="3" fill="white" opacity="0.95"/>
    
    <!-- Chair Base -->
    <rect x="7" y="23" width="2" height="6" fill="white" opacity="0.9"/>
    <ellipse cx="8" cy="30" rx="5" ry="1.5" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Company Name -->
  <text x="32" y="26" fill="white" font-family="Arial, sans-serif" font-size="14" font-weight="bold">SalonQuip</text>
</svg>

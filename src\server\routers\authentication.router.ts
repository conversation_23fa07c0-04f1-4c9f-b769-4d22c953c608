import { Configuration } from '@/core/configuration'
import { Trpc } from '@/core/trpc/server'
import { TRPCError } from '@trpc/server'
import * as Bcrypt from 'bcryptjs'
import { randomBytes } from 'crypto'
import * as Jwt from 'jsonwebtoken'
import { z } from 'zod'
import { EmailService } from '../libraries/email'

// Enhanced input validation schemas
const passwordSchema = z.string()
  .min(6, 'Password must have at least 6 characters')
  .max(100, 'Password is too long')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')

const userInputSchema = z.object({
  email: z.string().email('Invalid email format'),
  name: z.string().min(2, 'Name is too short').max(50, 'Name is too long'),
  pictureUrl: z.string().url().optional(),
  password: passwordSchema,
})

export const AuthenticationRouter = Trpc.createRouter({
  register: Trpc.procedurePublic
    .input(userInputSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if email is already registered
        const userExisting = await ctx.databaseUnprotected.user.findUnique({
          where: { email: input.email.toLowerCase() }, // Normalize email
          select: { id: true } // Only select needed fields
        })

        if (userExisting) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'Email is already registered',
          })
        }

        // Check if there is already an admin
        const adminCount = await ctx.databaseUnprotected.admin.count();
        if (adminCount > 0) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Registration is disabled. An admin already exists.',
          });
        }

        // Check if this is the first user
        const userCount = await ctx.databaseUnprotected.user.count();
        const isFirstUser = userCount === 0;

        // Hash password and create user
        const passwordHashed = await hashPassword(input.password)

        // Create user with appropriate role
        const userData = {
          ...input,
          email: input.email.toLowerCase(), // Normalize email
          password: passwordHashed,
        };

        const user = await ctx.databaseUnprotected.user.create({
          data: userData,
          select: { id: true } // Only return necessary data
        });

        // If this is the first user, make them an admin
        if (isFirstUser) {
          // Create admin role for the user
          await ctx.databaseUnprotected.role.create({
            data: {
              name: 'admin',
              userId: user.id
            }
          });

          // Create admin record
          await ctx.databaseUnprotected.admin.create({
            data: {
              userId: user.id
            }
          });

          console.info('First user registered as admin', { userId: user.id });
        } else {
          console.info('User registered as regular user', { userId: user.id });
        }

        return { id: user.id }
      } catch (error) {
        console.error('Registration failed', { error, email: input.email })
        throw error
      }
    }),

  sendResetPasswordEmail: Trpc.procedurePublic
    .input(z.object({ 
      email: z.string().email('Invalid email format') 
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        // Find user without throwing
        const user = await ctx.databaseUnprotected.user.findUnique({
          where: { email: input.email.toLowerCase() },
          select: { id: true, email: true, name: true }
        })

        // Don't reveal if user exists
        if (!user) {
          console.info('Reset password requested for non-existent user', { email: input.email })
          return { success: true }
        }

        const payload = { 
          userId: user.id,
          version: '1.0', // For future token versioning
          timestamp: Date.now()
        }

        const secret = Configuration.getAuthenticationSecret()
        const TIME_24_HOURS = 60 * 60 * 24

        const token = Jwt.sign(payload, secret, { 
          expiresIn: TIME_24_HOURS,
          algorithm: 'HS256' // Explicitly specify algorithm
        })

        const url = Configuration.getBaseUrl()
        const urlResetPassword = `${url}/reset-password/${token}`

        await EmailService.send({
          type: EmailService.Type.AUTHENTICATION_FORGOT_PASSWORD,
          email: user.email,
          name: user.name ?? user.email,
          subject: `Reset your password`,
          variables: {
            url_password_reset: urlResetPassword,
          },
        })

        console.info('Reset password email sent', { userId: user.id })
        return { success: true }

      } catch (error) {
        console.error('Failed to send reset password email', { 
          error,
          email: input.email 
        })
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Could not send the email',
        })
      }
    }),

  resetPassword: Trpc.procedurePublic
    .input(z.object({ 
      token: z.string(),
      password: passwordSchema
    }))
    .mutation(async ({ ctx, input }) => {
      try {
        const secret = Configuration.getAuthenticationSecret()

        // Verify and decode token
        const decoded = Jwt.verify(input.token, secret, {
          algorithms: ['HS256'] // Explicitly specify allowed algorithms
        }) as { userId: string; version: string; timestamp: number }

        // Check token age as additional security
        const tokenAge = Date.now() - decoded.timestamp
        if (tokenAge > 24 * 60 * 60 * 1000) { // 24 hours in milliseconds
          throw new Error('Token expired')
        }

        // Find and update user
        const user = await ctx.databaseUnprotected.user.findUnique({
          where: { id: decoded.userId },
          select: { id: true, email: true }
        })

        if (!user) {
          throw new Error('User not found')
        }

        const passwordHashed = await hashPassword(input.password)

        await ctx.databaseUnprotected.user.update({
          where: { id: user.id },
          data: {
            password: passwordHashed,
          },
        })

        console.info('Password reset successful', { userId: user.id })
        return { success: true }

      } catch (error) {
        console.error('Password reset failed', { error })
        
        if (error.message === 'Token expired') {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: 'Reset password link has expired',
          })
        }

        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid or expired reset password link',
        })
      }
    }),

  inviteAdmin: Trpc.procedure
    .input(z.object({ email: z.string().email('Invalid email format') }))
    .mutation(async ({ ctx, input }) => {
      // Only allow admins
      const userId = ctx.session?.user?.id
      if (!userId) {
        throw new TRPCError({ code: 'UNAUTHORIZED', message: 'Not authenticated' })
      }
      const isAdmin = await ctx.databaseUnprotected.role.findFirst({
        where: { userId, name: 'admin' },
      })
      if (!isAdmin) {
        throw new TRPCError({ code: 'FORBIDDEN', message: 'Only admins can invite other admins' })
      }

      // Check if invitation already exists and is pending
      const existing = await ctx.databaseUnprotected.adminInvitation.findFirst({
        where: { email: input.email, acceptedAt: null, expiresAt: { gt: new Date() } },
      })
      if (existing) {
        throw new TRPCError({ code: 'CONFLICT', message: 'An invitation for this email is already pending.' })
      }

      // Generate token and expiry
      const token = randomBytes(32).toString('hex')
      const expiresAt = new Date(Date.now() + 1000 * 60 * 60 * 24 * 3) // 3 days

      // Store invitation
      await ctx.databaseUnprotected.adminInvitation.create({
        data: {
          email: input.email,
          token,
          invitedBy: userId,
          expiresAt,
        },
      })

      // Send invitation email
      const url = Configuration.getBaseUrl()
      const inviteUrl = `${url}/register-admin?token=${token}`
      await EmailService.send({
        type: 'ADMIN_INVITATION', // You will need to implement this template
        email: input.email,
        subject: 'You are invited to become an admin',
        variables: {
          url_admin_invite: inviteUrl,
        },
      })

      return { success: true }
    }),

  acceptAdminInvitation: Trpc.procedurePublic
    .input(z.object({ token: z.string(), name: z.string(), password: passwordSchema }))
    .mutation(async ({ ctx, input }) => {
      // Find invitation
      const invitation = await ctx.databaseUnprotected.adminInvitation.findUnique({
        where: { token: input.token },
      })
      if (!invitation || invitation.acceptedAt || invitation.expiresAt < new Date()) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid or expired invitation token.' })
      }
      // Check if user already exists for this email
      const userExisting = await ctx.databaseUnprotected.user.findUnique({
        where: { email: invitation.email },
      })
      if (userExisting) {
        throw new TRPCError({ code: 'CONFLICT', message: 'A user with this email already exists.' })
      }
      // Hash password
      const passwordHashed = await hashPassword(input.password)
      // Create user
      const user = await ctx.databaseUnprotected.user.create({
        data: {
          email: invitation.email,
          name: input.name,
          password: passwordHashed,
        },
      })
      // Assign admin role
      await ctx.databaseUnprotected.role.create({
        data: {
          name: 'admin',
          userId: user.id,
        },
      })
      // Create admin record
      await ctx.databaseUnprotected.admin.create({
        data: {
          userId: user.id,
        },
      })
      // Mark invitation as accepted
      await ctx.databaseUnprotected.adminInvitation.update({
        where: { id: invitation.id },
        data: { acceptedAt: new Date() },
      })
      return { success: true }
    }),

  countAdmins: Trpc.procedurePublic
    .query(async ({ ctx }) => {
      try {
        // Count the number of admin records
        const count = await ctx.databaseUnprotected.admin.count();
        return count;
      } catch (error) {
        console.error('Failed to count admins', { error });
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Could not count admins',
        });
      }
    }),

  listAdminInvitations: Trpc.procedure
    .query(async ({ ctx }) => {
      // Only allow admins
      const userId = ctx.session?.user?.id
      if (!userId) throw new TRPCError({ code: 'UNAUTHORIZED' })
      const isAdmin = await ctx.databaseUnprotected.role.findFirst({ where: { userId, name: 'admin' } })
      if (!isAdmin) throw new TRPCError({ code: 'FORBIDDEN' })
      // Return all pending invitations
      return ctx.databaseUnprotected.adminInvitation.findMany({
        where: { acceptedAt: null, expiresAt: { gt: new Date() } },
        orderBy: { createdAt: 'desc' },
      })
    }),

  deleteAdminInvitation: Trpc.procedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Only allow admins
      const userId = ctx.session?.user?.id
      if (!userId) throw new TRPCError({ code: 'UNAUTHORIZED' })
      const isAdmin = await ctx.databaseUnprotected.role.findFirst({ where: { userId, name: 'admin' } })
      if (!isAdmin) throw new TRPCError({ code: 'FORBIDDEN' })
      // Delete invitation
      await ctx.databaseUnprotected.adminInvitation.delete({ where: { id: input.id } })
      return { success: true }
    }),

  resendAdminInvitation: Trpc.procedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Only allow admins
      const userId = ctx.session?.user?.id
      if (!userId) throw new TRPCError({ code: 'UNAUTHORIZED' })
      const isAdmin = await ctx.databaseUnprotected.role.findFirst({ where: { userId, name: 'admin' } })
      if (!isAdmin) throw new TRPCError({ code: 'FORBIDDEN' })
      // Find invitation
      const invitation = await ctx.databaseUnprotected.adminInvitation.findUnique({ where: { id: input.id } })
      if (!invitation || invitation.acceptedAt || invitation.expiresAt < new Date()) {
        throw new TRPCError({ code: 'BAD_REQUEST', message: 'Invalid or expired invitation.' })
      }
      // Resend invitation email
      const url = Configuration.getBaseUrl()
      const inviteUrl = `${url}/register-admin?token=${invitation.token}`
      await EmailService.send({
        type: 'ADMIN_INVITATION',
        email: invitation.email,
        subject: 'You are invited to become an admin',
        variables: {
          url_admin_invite: inviteUrl,
        },
      })
      return { success: true }
    }),
})

// Enhanced password hashing with Promise support
const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12 // Increased from 10 for better security
  try {
    const salt = await Bcrypt.genSalt(saltRounds)
    return await Bcrypt.hash(password, salt)
  } catch (error) {
    console.error('Password hashing failed', { error })
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Unable to process password',
    })
  }
}
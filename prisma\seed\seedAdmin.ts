import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import promptSync from 'prompt-sync';

const prisma = new PrismaClient();
const prompt = promptSync({ sigint: true });

async function main() {
  const adminCount = await prisma.admin.count();
  if (adminCount > 0) {
    console.log('An admin already exists. No action taken.');
    process.exit(0);
  }

  console.log('=== Admin Seeder ===');
  const name = prompt('Admin name: ');
  let email = '';
  while (true) {
    email = prompt('Admin email: ').toLowerCase().trim();
    if (/^[^@\s]+@[^@\s]+\.[^@\s]+$/.test(email)) break;
    console.log('Please enter a valid email address.');
  }
  let password = '';
  while (true) {
    password = prompt('Admin password: ', { echo: '*' });
    if (password.length >= 8) break;
    console.log('Password must be at least 8 characters.');
  }

  const hashed = await bcrypt.hash(password, 10);

  const user = await prisma.user.create({
    data: {
      name,
      email,
      password: hashed,
    },
  });

  await prisma.role.create({
    data: {
      name: 'admin',
      userId: user.id,
    },
  });

  await prisma.admin.create({
    data: {
      userId: user.id,
    },
  });

  console.log(`Admin user created: ${name} <${email}>`);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 
import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

async function createBackup() {
  try {
    console.log('🔄 Creating database backup...')

    const backup = {
      metadata: {
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        createdBy: 'CLI Script',
        createdByName: 'Command Line Interface',
      },
      data: {}
    }

    // Backup Users (excluding passwords)
    console.log('📋 Backing up users...')
    const users = await prisma.user.findMany({
      include: {
        roles: true,
        admins: true,
      },
      omit: {
        password: true
      }
    })
    backup.data.users = users
    console.log(`✅ ${users.length} users backed up`)

    // Backup Branches
    console.log('📋 Backing up branches...')
    const branches = await prisma.branch.findMany()
    backup.data.branches = branches
    console.log(`✅ ${branches.length} branches backed up`)

    // Backup Items
    console.log('📋 Backing up items...')
    const items = await prisma.item.findMany({
      include: {
        branch: true
      }
    })
    backup.data.items = items
    console.log(`✅ ${items.length} items backed up`)

    // Backup Customers
    console.log('📋 Backing up customers...')
    const customers = await prisma.customer.findMany()
    backup.data.customers = customers
    console.log(`✅ ${customers.length} customers backed up`)

    // Backup Sales
    console.log('📋 Backing up sales...')
    const sales = await prisma.sale.findMany({
      include: {
        saleItems: {
          include: {
            item: true
          }
        },
        customer: true,
        user: true
      }
    })
    backup.data.sales = sales
    console.log(`✅ ${sales.length} sales backed up`)

    // Backup Inventory Logs
    console.log('📋 Backing up inventory logs...')
    const inventoryLogs = await prisma.inventoryLog.findMany({
      include: {
        item: true,
        user: true
      }
    })
    backup.data.inventoryLogs = inventoryLogs
    console.log(`✅ ${inventoryLogs.length} inventory logs backed up`)

    // Backup Stock Transfers
    console.log('📋 Backing up stock transfers...')
    const stockTransfers = await prisma.stockTransfer.findMany({
      include: {
        item: true,
        fromBranch: true,
        toBranch: true
      }
    })
    backup.data.stockTransfers = stockTransfers
    console.log(`✅ ${stockTransfers.length} stock transfers backed up`)

    // Save backup to file
    const filename = `salonquip-backup-${new Date().toISOString().split('T')[0]}-${Date.now()}.json`
    const backupPath = path.join(process.cwd(), 'backups', filename)
    
    // Ensure backups directory exists
    const backupsDir = path.join(process.cwd(), 'backups')
    if (!fs.existsSync(backupsDir)) {
      fs.mkdirSync(backupsDir, { recursive: true })
    }

    fs.writeFileSync(backupPath, JSON.stringify(backup, null, 2))
    
    console.log('🎉 Backup completed successfully!')
    console.log(`📁 Backup saved to: ${backupPath}`)
    console.log(`📊 Backup contains:`)
    console.log(`   - ${users.length} users`)
    console.log(`   - ${branches.length} branches`)
    console.log(`   - ${items.length} items`)
    console.log(`   - ${customers.length} customers`)
    console.log(`   - ${sales.length} sales`)
    console.log(`   - ${inventoryLogs.length} inventory logs`)
    console.log(`   - ${stockTransfers.length} stock transfers`)

  } catch (error) {
    console.error('❌ Backup failed:', error.message)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

async function restoreBackup(backupFilePath) {
  try {
    console.log('🔄 Restoring database from backup...')
    
    if (!fs.existsSync(backupFilePath)) {
      throw new Error(`Backup file not found: ${backupFilePath}`)
    }

    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf-8'))
    
    if (!backupData.metadata || !backupData.data) {
      throw new Error('Invalid backup format')
    }

    console.log(`📅 Backup created: ${new Date(backupData.metadata.createdAt).toLocaleString()}`)
    console.log(`👤 Created by: ${backupData.metadata.createdByName}`)

    const results = {
      restored: {
        branches: 0,
        items: 0,
        customers: 0,
        sales: 0,
        users: 0
      },
      errors: []
    }

    // Restore Branches first (dependencies)
    if (backupData.data.branches) {
      console.log('📋 Restoring branches...')
      for (const branch of backupData.data.branches) {
        try {
          await prisma.branch.upsert({
            where: { id: branch.id },
            update: {
              name: branch.name,
              location: branch.location,
              dateUpdated: new Date()
            },
            create: {
              id: branch.id,
              name: branch.name,
              location: branch.location,
              dateCreated: new Date(branch.dateCreated),
              dateUpdated: new Date()
            }
          })
          results.restored.branches++
        } catch (error) {
          results.errors.push(`Branch ${branch.name}: ${error.message}`)
        }
      }
      console.log(`✅ ${results.restored.branches} branches restored`)
    }

    // Restore Customers
    if (backupData.data.customers) {
      console.log('📋 Restoring customers...')
      for (const customer of backupData.data.customers) {
        try {
          await prisma.customer.upsert({
            where: { id: customer.id },
            update: {
              name: customer.name,
              phoneNumber: customer.phoneNumber,
              loyaltyPoints: customer.loyaltyPoints,
              referredBy: customer.referredBy,
              dateUpdated: new Date()
            },
            create: {
              id: customer.id,
              name: customer.name,
              phoneNumber: customer.phoneNumber,
              loyaltyPoints: customer.loyaltyPoints,
              referredBy: customer.referredBy,
              dateCreated: new Date(customer.dateCreated),
              dateUpdated: new Date()
            }
          })
          results.restored.customers++
        } catch (error) {
          results.errors.push(`Customer ${customer.name}: ${error.message}`)
        }
      }
      console.log(`✅ ${results.restored.customers} customers restored`)
    }

    console.log('🎉 Restore completed!')
    if (results.errors.length > 0) {
      console.log('⚠️  Some errors occurred:')
      results.errors.forEach(error => console.log(`   - ${error}`))
    }

  } catch (error) {
    console.error('❌ Restore failed:', error.message)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

// CLI interface
const command = process.argv[2]
const backupFile = process.argv[3]

if (command === 'backup') {
  createBackup()
} else if (command === 'restore' && backupFile) {
  restoreBackup(backupFile)
} else {
  console.log('Usage:')
  console.log('  node scripts/backup.js backup                    # Create a backup')
  console.log('  node scripts/backup.js restore <backup-file>     # Restore from backup')
  process.exit(1)
}

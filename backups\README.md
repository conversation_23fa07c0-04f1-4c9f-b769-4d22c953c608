# SalonQuip Backups

This directory contains database backups for the SalonQuip application.

## Creating Backups

### Via Web Interface (Recommended)
1. Log in as an admin user
2. Navigate to Settings page
3. Use the "Create Backup" section to generate and download backups

### Via Command Line
```bash
# Create a backup
npm run database:backup

# Restore from a backup file
npm run database:restore path/to/backup-file.json
```

## Backup Format

Backups are stored as JSON files with the following structure:
- `metadata`: Information about when and by whom the backup was created
- `data`: All database tables and their contents

## Included Data

Backups include:
- Users (without passwords)
- Branches
- Items and inventory
- Customers
- Sales data
- Inventory logs
- Stock transfers
- Admin accounts and roles

## Security Notes

- Backup files do not contain user passwords
- Store backup files securely
- Backup files contain sensitive business data
- Regular backups are recommended

## Restore Process

When restoring:
1. Existing data can be preserved or cleared
2. Data is restored using upsert operations (update if exists, create if not)
3. Foreign key relationships are maintained
4. Detailed logs show what was restored and any errors

## File Naming Convention

Backup files are named: `salonquip-backup-YYYY-MM-DD-timestamp.json`

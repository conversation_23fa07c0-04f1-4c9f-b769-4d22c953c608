import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding salon equipment items...')

  // Get existing branches to assign items to
  const branches = await prisma.branch.findMany()
  
  if (branches.length === 0) {
    console.log('❌ No branches found. Please seed branches first.')
    return
  }

  console.log(`📍 Found ${branches.length} branches`)

  // Salon equipment categories and items
  const salonItems = [
    // Hair Care Equipment
    {
      name: 'Professional Hair Dryer - Dyson Supersonic',
      description: 'High-speed professional hair dryer with intelligent heat control',
      category: 'Hair Care',
      price: 450.00,
      sku: 'HAI001',
      quantity: 15,
      minimumStockLevel: 5,
      minimumSellPrice: 500.00,
      origin: 'UK',
      imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'
    },
    {
      name: 'Ceramic Hair Straightener - GHD Platinum+',
      description: 'Ultra-zone technology for healthier styling',
      category: 'Hair Care',
      price: 280.00,
      sku: 'HAI002',
      quantity: 8,
      minimumStockLevel: 3,
      minimumSellPrice: 320.00,
      origin: 'UK',
      imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'
    },
    {
      name: 'Professional Curling Iron Set',
      description: 'Multiple barrel sizes for versatile styling',
      category: 'Hair Care',
      price: 150.00,
      sku: 'HAI003',
      quantity: 2,
      minimumStockLevel: 5,
      minimumSellPrice: 180.00,
      origin: 'USA',
      imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'
    },
    {
      name: 'Hair Clipper Set - Wahl Professional',
      description: 'Complete professional hair cutting kit',
      category: 'Hair Care',
      price: 120.00,
      sku: 'HAI004',
      quantity: 12,
      minimumStockLevel: 4,
      minimumSellPrice: 150.00,
      origin: 'USA',
      imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'
    },
    {
      name: 'Salon Hair Washing Basin',
      description: 'Ergonomic shampoo bowl with adjustable height',
      category: 'Hair Care',
      price: 800.00,
      sku: 'HAI005',
      quantity: 0,
      minimumStockLevel: 2,
      minimumSellPrice: 950.00,
      origin: 'Italy',
      imageUrl: 'https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?w=400'
    },

    // Nail Care Equipment
    {
      name: 'UV LED Nail Lamp - 48W',
      description: 'Fast curing LED lamp for gel manicures',
      category: 'Nail Care',
      price: 80.00,
      sku: 'NAI001',
      quantity: 20,
      minimumStockLevel: 8,
      minimumSellPrice: 100.00,
      origin: 'China',
      imageUrl: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400'
    },
    {
      name: 'Electric Nail File System',
      description: 'Professional electric nail drill with multiple bits',
      category: 'Nail Care',
      price: 200.00,
      sku: 'NAI002',
      quantity: 6,
      minimumStockLevel: 3,
      minimumSellPrice: 250.00,
      origin: 'Germany',
      imageUrl: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400'
    },
    {
      name: 'Manicure Table with Storage',
      description: 'Professional manicure station with built-in storage',
      category: 'Nail Care',
      price: 350.00,
      sku: 'NAI003',
      quantity: 4,
      minimumStockLevel: 2,
      minimumSellPrice: 420.00,
      origin: 'China',
      imageUrl: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400'
    },
    {
      name: 'Nail Polish Display Rack',
      description: 'Rotating display rack for nail polish collection',
      category: 'Nail Care',
      price: 75.00,
      sku: 'NAI004',
      quantity: 1,
      minimumStockLevel: 3,
      minimumSellPrice: 95.00,
      origin: 'China',
      imageUrl: 'https://images.unsplash.com/photo-1604654894610-df63bc536371?w=400'
    },

    // Skin Care Equipment
    {
      name: 'Facial Steamer Professional',
      description: 'Hot mist facial steamer for deep cleansing',
      category: 'Skin Care',
      price: 180.00,
      sku: 'SKI001',
      quantity: 7,
      minimumStockLevel: 3,
      minimumSellPrice: 220.00,
      origin: 'South Korea',
      imageUrl: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400'
    },
    {
      name: 'Microdermabrasion Machine',
      description: 'Diamond tip microdermabrasion system',
      category: 'Skin Care',
      price: 1200.00,
      sku: 'SKI002',
      quantity: 2,
      minimumStockLevel: 1,
      minimumSellPrice: 1400.00,
      origin: 'USA',
      imageUrl: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400'
    },
    {
      name: 'LED Light Therapy Panel',
      description: 'Red and blue light therapy for skin treatment',
      category: 'Skin Care',
      price: 600.00,
      sku: 'SKI003',
      quantity: 3,
      minimumStockLevel: 2,
      minimumSellPrice: 750.00,
      origin: 'USA',
      imageUrl: 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400'
    },

    // Massage Equipment
    {
      name: 'Professional Massage Table',
      description: 'Adjustable height massage table with face cradle',
      category: 'Massage',
      price: 400.00,
      sku: 'MAS001',
      quantity: 5,
      minimumStockLevel: 2,
      minimumSellPrice: 480.00,
      origin: 'USA',
      imageUrl: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400'
    },
    {
      name: 'Hot Stone Heater',
      description: 'Professional hot stone massage heater',
      category: 'Massage',
      price: 250.00,
      sku: 'MAS002',
      quantity: 0,
      minimumStockLevel: 2,
      minimumSellPrice: 300.00,
      origin: 'China',
      imageUrl: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400'
    },

    // Salon Furniture
    {
      name: 'Hydraulic Salon Chair',
      description: 'Professional styling chair with hydraulic pump',
      category: 'Furniture',
      price: 650.00,
      sku: 'FUR001',
      quantity: 8,
      minimumStockLevel: 3,
      minimumSellPrice: 780.00,
      origin: 'Italy',
      imageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400'
    },
    {
      name: 'Salon Reception Desk',
      description: 'Modern reception desk with storage',
      category: 'Furniture',
      price: 1200.00,
      sku: 'FUR002',
      quantity: 2,
      minimumStockLevel: 1,
      minimumSellPrice: 1450.00,
      origin: 'Germany',
      imageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400'
    },
    {
      name: 'Salon Mirror with LED Lights',
      description: 'Hollywood-style mirror with adjustable LED lighting',
      category: 'Furniture',
      price: 300.00,
      sku: 'FUR003',
      quantity: 1,
      minimumStockLevel: 4,
      minimumSellPrice: 360.00,
      origin: 'China',
      imageUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400'
    },

    // Beauty Tools
    {
      name: 'Professional Makeup Brush Set',
      description: 'Complete 24-piece professional makeup brush collection',
      category: 'Beauty Tools',
      price: 120.00,
      sku: 'BEA001',
      quantity: 15,
      minimumStockLevel: 8,
      minimumSellPrice: 150.00,
      origin: 'Japan',
      imageUrl: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400'
    },
    {
      name: 'Eyelash Extension Kit',
      description: 'Complete kit for professional eyelash extensions',
      category: 'Beauty Tools',
      price: 200.00,
      sku: 'BEA002',
      quantity: 3,
      minimumStockLevel: 5,
      minimumSellPrice: 250.00,
      origin: 'South Korea',
      imageUrl: 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400'
    }
  ]

  // Distribute items across branches
  let itemCount = 0
  for (const itemData of salonItems) {
    const branchIndex = itemCount % branches.length
    const branch = branches[branchIndex]

    try {
      await prisma.item.create({
        data: {
          ...itemData,
          branchId: branch.id
        }
      })
      console.log(`✅ Created: ${itemData.name} at ${branch.name}`)
      itemCount++
    } catch (error) {
      console.log(`❌ Failed to create ${itemData.name}: ${error.message}`)
    }
  }

  console.log(`🎉 Successfully seeded ${itemCount} salon equipment items!`)
  console.log(`📊 Items distributed across ${branches.length} branches`)
  
  // Show summary by category
  const categories = [...new Set(salonItems.map(item => item.category))]
  console.log('\n📋 Items by category:')
  for (const category of categories) {
    const count = salonItems.filter(item => item.category === category).length
    console.log(`   ${category}: ${count} items`)
  }

  // Show low stock and out of stock items
  const lowStockItems = salonItems.filter(item => item.quantity < item.minimumStockLevel)
  const outOfStockItems = salonItems.filter(item => item.quantity === 0)
  
  console.log(`\n⚠️  Low stock items: ${lowStockItems.length}`)
  console.log(`🚫 Out of stock items: ${outOfStockItems.length}`)
}

main()
  .then(async () => {
    await prisma.$disconnect()
    console.log('✨ Seeding completed successfully!')
  })
  .catch(async (error) => {
    console.error('❌ Seeding failed:', error)
    await prisma.$disconnect()
    process.exit(1)
  })

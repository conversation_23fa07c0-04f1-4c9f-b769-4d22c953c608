/*
  Warnings:

  - Made the column `totalAmount` on table `Sale` required. This step will fail if there are existing NULL values in that column.
  - Made the column `totalProfit` on table `Sale` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `profit` to the `SaleItem` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Sale" DROP CONSTRAINT "Sale_itemId_fkey";

-- DropIndex
DROP INDEX "InventoryLog_action_idx";

-- DropIndex
DROP INDEX "InventoryLog_itemId_idx";

-- DropIndex
DROP INDEX "InventoryLog_userId_idx";

-- DropIndex
DROP INDEX "SaleItem_itemId_idx";

-- DropIndex
DROP INDEX "SaleItem_saleId_idx";

-- AlterTable
ALTER TABLE "Item" ALTER COLUMN "dateUpdated" DROP DEFAULT;

-- AlterTable
ALTER TABLE "Sale" ALTER COLUMN "totalAmount" SET NOT NULL,
ALTER COLUMN "totalProfit" SET NOT NULL,
ALTER COLUMN "profit" DROP NOT NULL;

-- AlterTable
ALTER TABLE "SaleItem" ADD COLUMN     "profit" DOUBLE PRECISION NOT NULL;
